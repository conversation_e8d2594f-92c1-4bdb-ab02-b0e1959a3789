/*
 * 最简单的NRF24L01发送测试
 * 专门解决发送失败问题
 * 避免所有复杂的API调用
 */

#include <SPI.h>
#include <RF24.h>

// 硬件连接
RF24 radio(9, 10);  // CE, CSN

void setup() {
    Serial.begin(115200);
    Serial.println(F("=== 最简单NRF24L01发送测试 ==="));
    Serial.println(F("专门解决发送失败问题"));
    Serial.println();
    
    delay(1000);  // 等待串口稳定
    
    // 检查硬件连接
    Serial.println(F("1. 检查硬件连接..."));
    printConnectionGuide();
    
    // 初始化NRF24L01
    Serial.println(F("2. 初始化NRF24L01..."));
    if (!radio.begin()) {
        Serial.println(F("❌ 初始化失败！"));
        Serial.println(F("请检查:"));
        Serial.println(F("- NRF24L01是否正确连接"));
        Serial.println(F("- 是否使用3.3V供电 (不是5V!)"));
        Serial.println(F("- SPI连线是否正确"));
        while(1) delay(1000);  // 停止运行
    }
    
    Serial.println(F("✅ 初始化成功"));
    
    // 使用最保守的设置
    Serial.println(F("3. 配置最保守参数..."));
    radio.setPALevel(RF24_PA_MIN);      // 最低功率 (-18dBm)
    radio.setDataRate(RF24_250KBPS);    // 最低速率
    radio.setChannel(40);               // 2440MHz
    radio.setAutoAck(false);            // 关闭自动应答 (重要!)
    radio.setRetries(0, 0);             // 关闭重传
    
    // 设置简单地址
    radio.openWritingPipe((uint8_t*)"TEST1");
    radio.stopListening();  // 发送模式
    
    Serial.println(F("✅ 配置完成"));
    Serial.println(F("参数设置:"));
    Serial.println(F("- 功率: 最低 (-18dBm)"));
    Serial.println(F("- 速率: 250kbps"));
    Serial.println(F("- 频道: 40 (2440MHz)"));
    Serial.println(F("- 自动应答: 关闭"));
    Serial.println(F("- 重传: 关闭"));
    Serial.println();
    
    Serial.println(F("4. 开始发送测试..."));
    Serial.println(F("每秒发送一次，观察结果"));
    Serial.println();
}

void loop() {
    static uint16_t counter = 0;
    
    // 准备发送数据
    uint8_t data = 0x55;  // 最简单的数据
    
    // 发送数据
    bool result = radio.write(&data, 1);
    
    // 显示结果
    Serial.print(F("包#"));
    Serial.print(counter);
    Serial.print(F(": "));
    
    if (result) {
        Serial.println(F("✅ 发送成功"));
        
        // 如果连续成功，说明问题解决了
        static uint8_t successCount = 0;
        successCount++;
        
        if (successCount >= 5) {
            Serial.println();
            Serial.println(F("🎉 连续5次发送成功！"));
            Serial.println(F("问题已解决，系统工作正常"));
            Serial.println(F("可以进行后续测试"));
            Serial.println();
            successCount = 0;  // 重置计数
        }
    } else {
        Serial.println(F("❌ 发送失败"));
        
        // 提供故障排除建议
        static uint8_t failCount = 0;
        failCount++;
        
        if (failCount == 5) {
            Serial.println();
            Serial.println(F("🚨 连续5次发送失败"));
            printTroubleshootingGuide();
            Serial.println();
            failCount = 0;  // 重置计数
        }
    }
    
    counter++;
    delay(1000);  // 每秒发送一次
}

void printConnectionGuide() {
    Serial.println(F("--- 硬件连接指南 ---"));
    Serial.println(F("NRF24L01 -> Arduino UNO R4"));
    Serial.println(F("VCC  -> 3.3V  (重要！不是5V)"));
    Serial.println(F("GND  -> GND"));
    Serial.println(F("CE   -> Pin 9"));
    Serial.println(F("CSN  -> Pin 10"));
    Serial.println(F("MOSI -> Pin 11"));
    Serial.println(F("MISO -> Pin 12"));
    Serial.println(F("SCK  -> Pin 13"));
    Serial.println();
    
    Serial.println(F("⚠️  重要提醒:"));
    Serial.println(F("1. 必须使用3.3V供电，5V会损坏模块"));
    Serial.println(F("2. 连线要短且牢固"));
    Serial.println(F("3. 建议添加去耦电容 (100uF + 10uF)"));
    Serial.println();
}

void printTroubleshootingGuide() {
    Serial.println(F("🔧 故障排除指南"));
    Serial.println();
    
    Serial.println(F("最可能的原因 - 电源供电不足:"));
    Serial.println(F("1. 检查3.3V电压 (用万用表测量应为3.0-3.6V)"));
    Serial.println(F("2. 在NRF24L01的VCC和GND之间添加电容:"));
    Serial.println(F("   - 100μF电解电容 (提供大电流)"));
    Serial.println(F("   - 10μF陶瓷电容 (滤除噪声)"));
    Serial.println(F("3. 如果还不行，使用外部3.3V稳压器"));
    Serial.println();
    
    Serial.println(F("其他可能原因:"));
    Serial.println(F("1. SPI连线错误或接触不良"));
    Serial.println(F("2. NRF24L01模块损坏"));
    Serial.println(F("3. Arduino 3.3V输出能力不足"));
    Serial.println(F("4. 天线连接问题"));
    Serial.println();
    
    Serial.println(F("立即检查清单:"));
    Serial.println(F("□ NRF24L01连接到3.3V (不是5V!)"));
    Serial.println(F("□ 所有SPI连线正确且牢固"));
    Serial.println(F("□ 添加了去耦电容"));
    Serial.println(F("□ 模块没有物理损坏"));
    Serial.println();
    
    Serial.println(F("如果以上都检查过仍然失败:"));
    Serial.println(F("1. 尝试更换NRF24L01模块"));
    Serial.println(F("2. 使用外部3.3V电源"));
    Serial.println(F("3. 检查Arduino UNO R4的3.3V输出"));
}

/*
 * 测试说明:
 * 
 * 成功标准:
 * - 看到 "✅ 发送成功" 表示硬件工作正常
 * - 连续5次成功表示系统稳定
 * 
 * 失败处理:
 * - 连续失败时会显示详细的故障排除指南
 * - 按照指南逐步检查硬件连接和供电
 * 
 * 最常见解决方案:
 * 1. 添加去耦电容 (100μF + 10μF)
 * 2. 确保3.3V供电稳定
 * 3. 检查SPI连线
 * 
 * 如果这个最简单的测试都失败，说明硬件有问题
 * 如果这个测试成功，可以进行更复杂的测试
 */
