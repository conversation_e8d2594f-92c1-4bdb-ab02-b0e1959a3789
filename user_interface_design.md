# 用户界面设计

## 1. 界面设计概述

### 1.1 设计原则
- **简洁直观**: 界面布局清晰，操作简单
- **信息层次**: 重要信息突出显示
- **实时反馈**: 操作结果即时反馈
- **状态可视**: 系统状态一目了然
- **容错设计**: 防误操作，安全第一

### 1.2 显示设备分工
- **TJC串口屏 (主显示)**: 触摸操作界面，训练管理，参数设置，数据分析
- **OLED显示屏 (副显示)**: 自动轮询显示射频状态、频谱图、系统监控
- **LED指示灯**: 系统状态快速指示
- **物理按键**: 仅保留总开关和紧急停止按键

## 2. TJC串口屏界面设计 (主显示)

### 2.1 主界面 (Main Menu)
```
┌─────────────────────────────────────────────────────────┐
│  电子对抗模拟训练系统 v1.0        [WiFi] [电池:85%]    │
├─────────────────────────────────────────────────────────┤
│                                                         │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │
│    │  基础训练   │  │  综合训练   │  │  对抗训练   │   │
│    │             │  │             │  │             │   │
│    │   [开始]    │  │   [开始]    │  │   [开始]    │   │
│    └─────────────┘  └─────────────┘  └─────────────┘   │
│                                                         │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │
│    │  系统设置   │  │  数据分析   │  │  帮助信息   │   │
│    │             │  │             │  │             │   │
│    │   [进入]    │  │   [查看]    │  │   [查看]    │   │
│    └─────────────┘  └─────────────┘  └─────────────┘   │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ 状态: 系统就绪  |  副控: 已连接  |  时间: 14:30:25    │
└─────────────────────────────────────────────────────────┘
```

### 2.2 训练模式选择界面 (触摸操作)
```
┌─────────────────────────────────────────────────────────┐
│  基础训练模式选择                          [返回主菜单] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ●○ 噪声干扰训练                          [选择]       │
│     频率: 2400-2500MHz  功率: 0-20dBm                  │
│                                                         │
│  ○○ 扫频干扰训练                          [选择]       │
│     扫描范围: 2400-2500MHz  扫描速度: 可调              │
│                                                         │
│  ○○ 脉冲干扰训练                          [选择]       │
│     脉冲宽度: 1-100ms  重复频率: 1-1000Hz               │
│                                                         │
│  ○○ 欺骗干扰训练                          [选择]       │
│     信号类型: WiFi/蓝牙  欺骗模式: 可选                 │
│                                                         │
│  ○○ 自定义训练                            [选择]       │
│     用户自定义参数                                      │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ [参数设置]  [开始训练]  [保存配置]  [加载配置]         │
└─────────────────────────────────────────────────────────┘
```
**注**: ●○表示已选中，○○表示未选中，所有按钮支持触摸操作

### 2.3 训练参数设置界面 (触摸滑块操作)
```
┌─────────────────────────────────────────────────────────┐
│  训练参数设置 - 噪声干扰                    [返回]     │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  中心频率: [2450] MHz        ┌─────────────────────┐    │
│  ┌─────────────────────────┐  │                     │    │
│  │ 2400  ●■■■■■■■■■  2500 │  │   实时频谱预览      │    │
│  └─────────────────────────┘  │                     │    │
│  ↑ 触摸拖动调节              │                     │    │
│  输出功率: [10] dBm           │                     │    │
│  ┌─────────────────────────┐  │                     │    │
│  │  0   ●■■■■■■■■■   20  │  │                     │    │
│  └─────────────────────────┘  └─────────────────────┘    │
│  ↑ 触摸拖动调节                                        │
│  带宽设置: [20] MHz                                     │
│  ┌─────────────────────────┐                            │
│  │  1   ●■■■■■■■■■   100 │                            │
│  └─────────────────────────┘                            │
│  ↑ 触摸拖动调节                                        │
│  训练时长: [60] 秒                                      │
│  ┌─────────────────────────┐                            │
│  │ 10   ●■■■■■■■■■   300 │                            │
│  └─────────────────────────┘                            │
│  ↑ 触摸拖动调节                                        │
├─────────────────────────────────────────────────────────┤
│ [默认值]  [保存]  [测试]  [开始训练]                   │
└─────────────────────────────────────────────────────────┘
```
**注**: ●表示滑块位置，支持触摸拖动实时调节参数

### 2.4 训练进行界面
```
┌─────────────────────────────────────────────────────────┐
│  训练进行中 - 噪声干扰                      [紧急停止] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  训练进度: ████████████████████████████████████░░░░ 85% │
│  剩余时间: 00:09                                        │
│                                                         │
│  当前参数:                    实时状态:                │
│  ├ 频率: 2450 MHz           ├ 发射功率: 9.8 dBm        │
│  ├ 功率: 10 dBm             ├ 温度: 45°C               │
│  ├ 带宽: 20 MHz             ├ 电流: 180 mA             │
│  └ 模式: 噪声干扰           └ 副控状态: 正常            │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │                实时频谱图                       │   │
│  │  dBm                                            │   │
│  │   0 ┌─┐                                         │   │
│  │ -10 │ │ ┌─┐                                     │   │
│  │ -20 │ │ │ │ ┌─┐                                 │   │
│  │ -30 │ │ │ │ │ │                                 │   │
│  │ -40 └─┘ └─┘ └─┘                                 │   │
│  │     2.4  2.45  2.5 GHz                         │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ [暂停]  [调整参数]  [保存数据]  [查看日志]             │
└─────────────────────────────────────────────────────────┘
```

### 2.5 数据分析界面
```
┌─────────────────────────────────────────────────────────┐
│  训练数据分析                              [导出数据]   │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  训练记录: 2024-01-15 14:30:25                          │
│  训练模式: 噪声干扰  持续时间: 60秒                     │
│  成功率: 95%  评分: A                                   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │              功率变化曲线                       │   │
│  │  dBm                                            │   │
│  │  20 ┌─────────────────────────────────────────┐ │   │
│  │  15 │     ┌─────┐                             │ │   │
│  │  10 │ ┌───┘     └─────┐                       │ │   │
│  │   5 │ │               └───┐                   │ │   │
│  │   0 └─┘                   └───────────────────┘ │   │
│  │     0    15    30    45    60 (秒)             │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  统计信息:                    性能指标:                │
│  ├ 平均功率: 12.5 dBm        ├ 响应时间: 15ms          │
│  ├ 最大功率: 18.2 dBm        ├ 稳定性: 98%             │
│  ├ 最小功率: 8.1 dBm         ├ 精度: 0.1dBm            │
│  └ 功率方差: 2.3 dB          └ 干扰效果: 优秀          │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ [上一条]  [下一条]  [删除]  [详细报告]                 │
└─────────────────────────────────────────────────────────┘
```

## 3. OLED显示屏界面设计 (副显示 - 自动轮询)

### 3.1 页面1: 射频状态显示 (5秒显示)
```
┌──────────────────────────────┐
│ RF Status    [●] 14:30:25    │
├──────────────────────────────┤
│ Freq: 2450.0 MHz             │
│ Power: 10.2 dBm              │
│ Mode: NOISE_JAM              │
│ Temp: 45°C                   │
├──────────────────────────────┤
│ ████████████████████████████ │ ← 功率条
│ ████████████████████████████ │
├──────────────────────────────┤
│ TX: ON  │ Err: 0  │ Link: OK │
└──────────────────────────────┘
```

### 3.2 页面2: 频谱显示 (5秒显示)
```
┌──────────────────────────────┐
│ Spectrum Analyzer    [2/4]   │
├──────────────────────────────┤
│  0dBm ┌─┐                    │
│ -10dBm │ │ ┌─┐                │
│ -20dBm │ │ │ │ ┌─┐            │
│ -30dBm │ │ │ │ │ │            │
│ -40dBm └─┘ └─┘ └─┘            │
├──────────────────────────────┤
│ 2.4G   2.45G   2.5G          │
├──────────────────────────────┤
│ Peak: 2450MHz @ -5dBm        │
└──────────────────────────────┘
```

### 3.3 页面3: 系统监控 (5秒显示)
```
┌──────────────────────────────┐
│ System Monitor       [3/4]   │
├──────────────────────────────┤
│ CPU: 45%    RAM: 1.2/2.0KB   │
│ Uptime: 02:15:30             │
│ Battery: 85% (7.2V)          │
│ Temperature: 45°C            │
├──────────────────────────────┤
│ Master Link: ████████████    │
│ Signal Quality: 95%          │
├──────────────────────────────┤
│ Status: All Systems Normal   │
└──────────────────────────────┘
```

### 3.4 页面4: 训练统计 (5秒显示)
```
┌──────────────────────────────┐
│ Training Stats       [4/4]   │
├──────────────────────────────┤
│ Current Session:             │
│ Duration: 00:15:30           │
│ Packets Sent: 1,250         │
│ Success Rate: 98.5%          │
├──────────────────────────────┤
│ Today's Total:               │
│ Sessions: 3                  │
│ Total Time: 01:45:20         │
├──────────────────────────────┤
│ Auto-cycling in 3s...        │
└──────────────────────────────┘
```

### 3.5 错误/警告显示 (优先级最高，立即显示)
```
┌──────────────────────────────┐
│ ⚠ WARNING ⚠         [ALERT] │
├──────────────────────────────┤
│                              │
│   Temperature High!          │
│   Current: 65°C              │
│   Limit: 60°C                │
│                              │
│   Auto Reducing Power...     │
│                              │
├──────────────────────────────┤
│ Will resume cycling in 10s   │
└──────────────────────────────┘
```

**轮询逻辑**: 正常情况下按页面1→2→3→4循环显示，每页5秒。出现警告时立即切换到警告页面，警告解除后恢复正常轮询。

## 4. LED指示灯设计

### 4.1 主控LED指示
| LED | 颜色 | 状态 | 含义 |
|-----|------|------|------|
| PWR | 绿色 | 常亮 | 系统电源正常 |
| SYS | 蓝色 | 闪烁 | 系统运行正常 |
| COMM | 黄色 | 闪烁 | 通信活动指示 |
| ERR | 红色 | 常亮/闪烁 | 错误/警告状态 |

### 4.2 副控LED指示
| LED | 颜色 | 状态 | 含义 |
|-----|------|------|------|
| RF | 绿色 | 常亮 | 射频模块正常 |
| TX | 红色 | 闪烁 | 发射状态 |
| LINK | 蓝色 | 常亮 | 与主控连接正常 |
| WARN | 黄色 | 闪烁 | 警告状态 |

## 5. 物理按键设计 (仅关键控制)

### 5.1 物理按键功能
| 按键 | 短按 | 长按 | 说明 |
|------|------|------|------|
| 总开关 | 系统开/关 | - | 硬件电源控制 |
| 紧急停止 | 立即停止所有射频输出 | 系统复位 | 安全保护功能 |

### 5.2 按键响应设计
```cpp
// 按键状态定义
enum ButtonState {
    BUTTON_IDLE,
    BUTTON_PRESSED,
    BUTTON_HELD,
    BUTTON_RELEASED
};

// 关键按键处理逻辑
void handleCriticalButtons() {
    // 紧急停止按键检测
    if(digitalRead(EMERGENCY_STOP_PIN) == LOW) {
        // 立即停止所有射频输出
        emergencyStop();
        // 显示紧急停止状态
        displayEmergencyStop();
    }

    // 总开关检测
    if(digitalRead(POWER_SWITCH_PIN) == LOW) {
        // 安全关机流程
        safeShutdown();
    }
}

// 触摸屏事件处理
void handleTouchEvent(uint8_t pageId, uint8_t componentId) {
    // 根据页面和组件ID处理触摸事件
    switch(pageId) {
        case PAGE_MAIN_MENU:
            handleMainMenuTouch(componentId);
            break;
        case PAGE_TRAINING_SELECT:
            handleTrainingSelectTouch(componentId);
            break;
        // ... 其他页面处理
    }
}
```

## 6. 界面切换逻辑

### 6.1 界面状态机
```
主菜单 ←→ 训练选择 ←→ 参数设置 ←→ 训练进行
  ↓         ↓           ↓           ↓
系统设置   数据分析    测试模式    训练完成
  ↓         ↓           ↓           ↓
各子设置   详细报告    调试界面    结果显示
```

### 6.2 界面切换代码框架
```cpp
class UIStateMachine {
private:
    UIState currentState;
    UIState previousState;
    
public:
    void changeState(UIState newState);
    void returnToPrevious();
    void handleStateTransition();
    void updateCurrentUI();
};

enum UIState {
    UI_MAIN_MENU,
    UI_TRAINING_SELECT,
    UI_PARAMETER_SETUP,
    UI_TRAINING_RUNNING,
    UI_DATA_ANALYSIS,
    UI_SYSTEM_SETTINGS
};
```

## 7. 用户体验优化

### 7.1 响应性设计
- 按键响应时间 < 100ms
- 界面切换动画 < 300ms
- 数据更新频率: 200ms
- 错误提示即时显示

### 7.2 易用性设计
- **触摸操作优化**: 按钮大小适合手指操作，触摸反馈明确
- **参数调节直观**: 滑块拖动实时显示数值变化
- **智能默认值**: 根据训练模式自动设置合理参数
- **操作提示**: 界面提供操作指导和帮助信息
- **二次确认**: 重要操作（如开始训练、删除数据）需要确认

### 7.3 安全性设计
- **硬件紧急停止**: 物理按键立即切断射频输出
- **软件安全检查**: 参数越限自动保护和警告
- **状态监控**: OLED自动轮询显示系统状态
- **触摸锁定**: 训练进行时可锁定触摸屏防误操作
