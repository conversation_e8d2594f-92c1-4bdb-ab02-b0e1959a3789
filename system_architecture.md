# 便携式电子对抗模拟训练系统设计方案

## 1. 系统概述

本系统是一个基于Arduino平台的便携式电子对抗模拟训练系统，采用主副控双MCU架构，实现2.4G频段的电子对抗模拟训练功能。

## 2. 硬件架构

### 2.1 主控系统 (Arduino UNO R4 WiFi)
**功能定位：** 系统总控制器、人机交互、数据管理
**连接模块：**
- TJC8048X570_011C 串口屏 (主显示界面)
- NRF24L01+PA+LNA 无线模块 (与副控通信)
- WiFi模块 (远程控制和数据传输)

**主要功能：**
- 训练场景设置和管理
- 系统参数配置
- 训练数据记录和分析
- 远程控制接口
- 与副控的指令协调

### 2.2 副控系统 (Arduino MEGA 2560)
**功能定位：** 射频信号处理、电子对抗执行
**连接模块：**
- VCO压控振荡器 (信号生成)
- 带通滤波器 (信号调理)
- 功率放大器 (信号放大)
- MCP4725 DAC模块 (模拟信号输出)
- NRF24L01+PA+LNA 无线模块 (与主控通信)
- 0.96" OLED (SSD1315) (状态显示)

**主要功能：**
- 2.4G射频信号生成
- 电子对抗信号模拟
- 实时信号处理
- 干扰信号生成
- 本地状态监控

## 3. 系统工作原理

### 3.1 信号处理链路
```
MCP4725 DAC → RFVCO-2400 → 衰减器(3-6dB) → 带通滤波器(2402-2461MHz) → PA模块(1W) → 天线输出
控制电压: 0-4.5V   输出: ≥7dBm    降至: ≤13dBm     滤波: 59MHz带宽    增益: 28dB   最大: 30dBm
```

### 3.2 实际硬件参数
- **VCO频率范围**: 2300-2650MHz (实际使用2402-2461MHz)
- **滤波器通带**: 2402-2461MHz (59MHz带宽)
- **PA输入限制**: 最大13dBm，需要衰减器保护
- **系统输出功率**: 最大30dBm (1W)

### 3.2 控制流程
1. 主控接收用户指令或远程命令
2. 主控通过NRF24L01发送控制指令给副控
3. 副控执行具体的射频操作
4. 副控反馈执行状态给主控
5. 主控更新显示界面和记录数据

## 4. 功能模块设计

### 4.1 电子对抗模拟功能
- **干扰信号生成：** 噪声干扰、欺骗干扰、压制干扰
- **频率扫描：** 2.4G频段扫描和监测
- **信号分析：** 接收信号强度和频谱分析
- **对抗效果评估：** 干扰效果量化评估

### 4.2 训练场景模式
- **基础训练模式：** 单一干扰类型训练
- **综合训练模式：** 多种干扰组合训练
- **对抗训练模式：** 攻防对抗模拟
- **自定义模式：** 用户自定义参数训练

### 4.3 人机交互界面
- **主显示屏 (TJC串口屏)：** 
  - 系统状态总览
  - 训练场景选择
  - 参数设置界面
  - 数据分析图表
  
- **副显示屏 (OLED)：**
  - 射频模块状态
  - 实时频谱显示
  - 功率输出指示
  - 告警信息显示

## 5. 技术特点

### 5.1 系统优势
- **模块化设计：** 各功能模块独立，便于维护和升级
- **双MCU架构：** 主副控分工明确，提高系统稳定性
- **无线通信：** NRF24L01实现主副控无线连接
- **远程控制：** WiFi功能支持远程监控和控制
- **便携性强：** 紧凑设计，适合野外训练使用

### 5.2 安全特性
- **功率限制：** 严格控制射频输出功率，符合法规要求
- **频段限制：** 仅在允许的ISM频段内工作
- **紧急停止：** 硬件和软件双重紧急停止机制
- **状态监控：** 实时监控系统运行状态

## 6. 应用场景

### 6.1 训练应用
- 电子对抗基础理论教学
- 干扰与反干扰技术训练
- 频谱管理和监测训练
- 电子战术演练

### 6.2 技术验证
- 电子对抗算法验证
- 新型干扰技术测试
- 抗干扰能力评估
- 系统性能测试

## 7. 扩展性设计

### 7.1 硬件扩展
- 支持更多频段模块接入
- 可扩展更多传感器
- 支持外部功率放大器
- 预留调试和测试接口

### 7.2 软件扩展
- 模块化软件架构
- 标准化通信协议
- 开放式API接口
- 支持固件在线升级
