/*
 * 副控系统配置文件 (Arduino MEGA 2560)
 * 包含所有硬件配置、系统参数和常量定义
 */

#ifndef SLAVE_CONFIG_H
#define SLAVE_CONFIG_H

#include <Arduino.h>

// ================================
// 系统版本信息
// ================================
#define SLAVE_SYSTEM_VERSION "1.0.0"
#define SLAVE_BUILD_DATE __DATE__
#define SLAVE_BUILD_TIME __TIME__

// ================================
// 硬件平台配置
// ================================
#define SLAVE_BOARD_MEGA_2560

// ================================
// 副控引脚配置 (Arduino MEGA 2560)
// ================================

// 串口通信 (与主控)
#define MASTER_RX_PIN 18       // 接收主控数据的引脚 (MEGA 2560)
#define MASTER_TX_PIN 19       // 发送数据到主控的引脚 (MEGA 2560)
#define MASTER_BAUD_RATE 9600  // SoftwareSerial推荐波特率

// NRF24L01 信号侦察模块
#define RF_CE_PIN 9
#define RF_CSN_PIN 10
// SPI引脚: MOSI=51, MISO=50, SCK=52 (MEGA 2560默认)

// VCO控制 (MCP4725 DAC)
#define DAC_I2C_ADDRESS 0x60

// 功放控制
#define AMPLIFIER_PWM_PIN 6    // PWM控制功放功率
#define AMPLIFIER_ENABLE_PIN 7 // 功放使能控制

// 滤波器控制 (GPIO控制继电器)
#define FILTER_CTRL_PIN1 22    // 滤波器控制位1
#define FILTER_CTRL_PIN2 23    // 滤波器控制位2
#define FILTER_CTRL_PIN3 24    // 滤波器控制位3

// OLED显示屏 (SSD1315, 0.96寸黄蓝双色)
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1
// I2C引脚: SDA=20, SCL=21 (MEGA 2560默认)
#define OLED_I2C_ADDRESS 0x3C

// 状态LED
#define STATUS_LED_READY 25    // 就绪状态LED (绿色)
#define STATUS_LED_JAMMING 26  // 干扰状态LED (红色)
#define STATUS_LED_SCANNING 27 // 侦察状态LED (蓝色)

// ================================
// 系统配置
// ================================

// 通信协议
#define COMM_START_BYTE 0xAA
#define COMM_END_BYTE 0x55
#define COMM_MAX_DATA_LEN 16

// VCO配置 (基于KVCO-2400实测数据)
#define VCO_FREQ_MIN 2399.26   // MHz
#define VCO_FREQ_MAX 2460.46   // MHz
#define VCO_VOLTAGE_MIN 0.7    // V
#define VCO_VOLTAGE_MAX 1.5    // V

// 滤波器配置
#define FILTER_CENTER_FREQ 2431.5  // MHz
#define FILTER_BANDWIDTH 59        // MHz

// 功放配置
#define AMPLIFIER_MAX_POWER 34.5   // dBm (2.5W)
#define AMPLIFIER_GAIN 30          // dB

// ================================
// 调试配置
// ================================
#define SLAVE_DEBUG_MODE 1
#define SLAVE_SERIAL_DEBUG_BAUD 115200

#if SLAVE_DEBUG_MODE
#define SLAVE_DEBUG_PRINT(x) Serial.print(x)
#define SLAVE_DEBUG_PRINTLN(x) Serial.println(x)
#define SLAVE_DEBUG_PRINTF(fmt, ...) Serial.printf(fmt, __VA_ARGS__)
#else
#define SLAVE_DEBUG_PRINT(x)
#define SLAVE_DEBUG_PRINTLN(x)
#define SLAVE_DEBUG_PRINTF(fmt, ...)
#endif

// ================================
// 错误代码定义
// ================================
#define SLAVE_ERROR_NONE 0x0000
#define SLAVE_ERROR_INIT 0x0001
#define SLAVE_ERROR_COMM 0x0002
#define SLAVE_ERROR_RF 0x0003
#define SLAVE_ERROR_DAC 0x0004
#define SLAVE_ERROR_VCO 0x0005
#define SLAVE_ERROR_AMPLIFIER 0x0006
#define SLAVE_ERROR_FILTER 0x0007
#define SLAVE_ERROR_OLED 0x0008

#endif // SLAVE_CONFIG_H