# 软件架构设计

## 1. 整体软件架构

### 1.1 架构概述
```
应用层 (Application Layer)
├── 训练管理模块
├── 用户界面模块
├── 数据分析模块
└── 远程控制模块

中间件层 (Middleware Layer)
├── 通信管理模块
├── 设备驱动模块
├── 状态管理模块
└── 错误处理模块

硬件抽象层 (HAL Layer)
├── GPIO控制
├── 串口通信
├── I2C通信
├── SPI通信
└── PWM输出
```

### 1.2 模块化设计原则
- **高内聚低耦合**: 各模块功能独立，接口清晰
- **分层架构**: 应用层、中间件层、硬件抽象层
- **事件驱动**: 基于事件的异步处理机制
- **状态机**: 复杂逻辑采用状态机实现
- **可配置**: 参数配置化，便于调试和维护

## 2. 主控软件架构 (Arduino UNO R4 WiFi)

### 2.1 主要功能模块

#### 2.1.1 系统管理模块 (SystemManager)
```cpp
class SystemManager {
private:
    SystemState currentState;
    uint32_t systemUptime;
    
public:
    void init();
    void update();
    void setState(SystemState state);
    SystemState getState();
    void handleSystemEvent(SystemEvent event);
};

enum SystemState {
    SYSTEM_INIT,
    SYSTEM_READY,
    SYSTEM_RUNNING,
    SYSTEM_ERROR,
    SYSTEM_SHUTDOWN
};
```

#### 2.1.2 通信管理模块 (CommManager)
```cpp
class CommManager {
private:
    NRF24L01 radio;
    WiFiManager wifi;
    SerialManager serial;
    
public:
    void init();
    void sendCommand(uint8_t cmdId, uint8_t* data, uint8_t len);
    bool receiveData(CommPacket& packet);
    void handleWiFiCommand(String command);
    void updateHeartbeat();
};
```

#### 2.1.3 用户界面模块 (UIManager)
```cpp
class UIManager {
private:
    TJCDisplay display;
    LEDManager leds;
    uint8_t currentPage;
    bool touchLocked;

public:
    void init();
    void updateDisplay();
    void handleTouchEvent(uint8_t pageId, uint8_t componentId);
    void handleCriticalButtons();
    void showStatus(SystemStatus status);
    void showTrainingData(TrainingData data);
    void lockTouch(bool locked);
    void switchPage(uint8_t pageId);
};
```

#### 2.1.4 训练管理模块 (TrainingManager)
```cpp
class TrainingManager {
private:
    TrainingMode currentMode;
    TrainingScenario scenario;
    TrainingData data;
    
public:
    void init();
    void startTraining(TrainingMode mode);
    void stopTraining();
    void updateTrainingData(TrainingData newData);
    void saveTrainingResult();
};

enum TrainingMode {
    MODE_BASIC_JAMMING,
    MODE_SWEEP_JAMMING,
    MODE_DECEPTION_JAMMING,
    MODE_CUSTOM
};
```

### 2.2 主控程序流程
```cpp
void setup() {
    // 系统初始化
    SystemManager.init();
    CommManager.init();
    UIManager.init();
    TrainingManager.init();
    
    // 建立与副控的通信
    CommManager.connectToSlave();
    
    // 显示启动界面
    UIManager.showStartupScreen();
}

void loop() {
    // 系统状态更新
    SystemManager.update();
    
    // 处理通信数据
    CommManager.processIncomingData();
    
    // 更新用户界面
    UIManager.update();
    
    // 处理训练逻辑
    TrainingManager.update();
    
    // 处理WiFi命令
    CommManager.processWiFiCommands();
    
    delay(10); // 10ms主循环
}
```

## 3. 副控软件架构 (Arduino MEGA 2560)

### 3.1 主要功能模块

#### 3.1.1 射频控制模块 (RFController)
```cpp
class RFController {
private:
    VCOController vco;
    FilterController filter;
    AmplifierController amplifier;
    MCP4725 dac;
    
public:
    void init();
    void setFrequency(uint32_t freq);
    void setPower(uint16_t power);
    void startTransmission();
    void stopTransmission();
    void setJammingMode(JammingMode mode);
};

enum JammingMode {
    JAMMING_NOISE,
    JAMMING_SWEEP,
    JAMMING_PULSE,
    JAMMING_DECEPTION
};
```

#### 3.1.2 信号处理模块 (SignalProcessor)
```cpp
class SignalProcessor {
private:
    uint32_t currentFreq;
    uint16_t currentPower;
    JammingMode currentMode;
    
public:
    void init();
    void generateNoiseJamming();
    void generateSweepJamming();
    void generatePulseJamming();
    void generateDeceptionJamming();
    void processSignal();
};
```

#### 3.1.3 本地显示模块 (LocalDisplay)
```cpp
class LocalDisplay {
private:
    SSD1315_OLED oled;
    uint8_t currentPage;
    unsigned long lastPageSwitch;
    bool alertMode;
    uint8_t totalPages;

public:
    void init();
    void update();  // 自动轮询更新
    void showRFStatus();      // 页面1: 射频状态
    void showSpectrum();      // 页面2: 频谱显示
    void showSystemMonitor(); // 页面3: 系统监控
    void showTrainingStats(); // 页面4: 训练统计
    void showAlert(uint16_t errorCode); // 警告页面
    void nextPage();
    void setAlertMode(bool alert);
};
```

#### 3.1.4 通信接口模块 (CommInterface)
```cpp
class CommInterface {
private:
    NRF24L01 radio;
    CommandQueue cmdQueue;
    
public:
    void init();
    void processCommands();
    void sendStatus();
    void sendData(uint8_t* data, uint8_t len);
    bool receiveCommand(CommPacket& packet);
};
```

### 3.2 副控程序流程
```cpp
void setup() {
    // 硬件初始化
    RFController.init();
    SignalProcessor.init();
    LocalDisplay.init();
    CommInterface.init();
    
    // 显示启动信息
    LocalDisplay.showStartup();
    
    // 等待主控连接
    CommInterface.waitForMaster();
}

void loop() {
    // 处理通信命令
    CommInterface.processCommands();
    
    // 执行射频控制
    RFController.update();
    
    // 信号处理
    SignalProcessor.update();
    
    // 更新本地显示
    LocalDisplay.update();
    
    // 发送状态数据
    CommInterface.sendStatusUpdate();
    
    delay(5); // 5ms主循环
}
```

## 4. 状态机设计

### 4.1 系统状态机
```
系统状态转换图:
INIT → READY → RUNNING → READY
  ↓      ↓        ↓        ↓
ERROR ← ERROR ← ERROR ← ERROR
  ↓
SHUTDOWN
```

### 4.2 训练状态机
```
训练状态转换图:
IDLE → SETUP → RUNNING → PAUSED → RUNNING → COMPLETED → IDLE
 ↑       ↓        ↓        ↓         ↓         ↓         ↓
 ←─────ERROR ←─ ERROR ←─ ERROR ←─ ERROR ←─ ERROR ←─ ERROR
```

## 5. 数据结构定义

### 5.1 通信数据包
```cpp
typedef struct {
    uint8_t header;      // 包头 0xAA
    uint8_t seqNum;      // 序列号
    uint8_t cmdId;       // 命令ID
    uint8_t length;      // 数据长度
    uint8_t data[24];    // 数据载荷
    uint32_t crc;        // CRC校验
} CommPacket;
```

### 5.2 系统状态数据
```cpp
typedef struct {
    SystemState state;
    uint32_t uptime;
    uint16_t batteryVoltage;
    int8_t temperature;
    uint8_t errorCode;
    uint32_t timestamp;
} SystemStatus;
```

### 5.3 射频参数数据
```cpp
typedef struct {
    uint32_t frequency;     // 频率 (Hz)
    uint16_t power;         // 功率 (dBm)
    JammingMode mode;       // 干扰模式
    uint16_t bandwidth;     // 带宽 (Hz)
    uint8_t duration;       // 持续时间 (s)
} RFParameters;
```

### 5.4 训练数据
```cpp
typedef struct {
    uint16_t scenarioId;    // 场景ID
    uint32_t startTime;     // 开始时间
    uint32_t duration;      // 持续时间
    RFParameters rfParams;  // 射频参数
    uint16_t successRate;   // 成功率
    uint8_t score;          // 评分
} TrainingData;
```

## 6. 中断处理设计

### 6.1 主控中断处理
```cpp
// NRF24L01数据接收中断
void onRadioReceive() {
    CommManager.handleRadioInterrupt();
}

// 紧急停止按键中断 (高优先级)
void onEmergencyStop() {
    SystemManager.emergencyStop();
    UIManager.showEmergencyStop();
}

// 触摸屏数据接收中断
void onTouchReceive() {
    UIManager.handleTouchInterrupt();
}

// 定时器中断 (1ms)
void onTimerTick() {
    SystemManager.updateSystemTick();
}
```

### 6.2 副控中断处理
```cpp
// NRF24L01数据接收中断
void onRadioReceive() {
    CommInterface.handleRadioInterrupt();
}

// ADC转换完成中断
void onADCComplete() {
    SignalProcessor.handleADCInterrupt();
}

// 定时器中断 (射频控制)
void onRFTimer() {
    RFController.handleTimerInterrupt();
}
```

## 7. 内存管理

### 7.1 内存分配策略
- **静态分配**: 系统关键数据结构
- **栈分配**: 临时变量和函数参数
- **避免动态分配**: 防止内存碎片

### 7.2 内存使用估算
```
主控 (UNO R4 WiFi):
- 程序存储: ~24KB (Flash 32KB)
- 运行内存: ~1.5KB (SRAM 2KB)
- 预留空间: ~25%

副控 (MEGA 2560):
- 程序存储: ~32KB (Flash 256KB)
- 运行内存: ~4KB (SRAM 8KB)
- 预留空间: ~50%
```

## 8. 调试和测试

### 8.1 调试接口
```cpp
#ifdef DEBUG_MODE
#define DEBUG_PRINT(x) Serial.print(x)
#define DEBUG_PRINTLN(x) Serial.println(x)
#else
#define DEBUG_PRINT(x)
#define DEBUG_PRINTLN(x)
#endif
```

### 8.2 单元测试框架
```cpp
class TestFramework {
public:
    static void runAllTests();
    static void testCommManager();
    static void testRFController();
    static void testSignalProcessor();
    static void reportResults();
};
```
