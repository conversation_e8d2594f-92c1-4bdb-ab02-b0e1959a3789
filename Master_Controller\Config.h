/*
 * 系统配置文件
 * 包含所有硬件配置、系统参数和常量定义
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// ================================
// 系统版本信息
// ================================
#define SYSTEM_VERSION "1.0.0"
#define BUILD_DATE __DATE__
#define BUILD_TIME __TIME__

// ================================
// 硬件平台配置
// ================================
#define MASTER_BOARD_UNO_R4_WIFI
#define SLAVE_BOARD_MEGA_2560

// ================================
// 主控引脚配置 (Arduino UNO R4 WiFi + 扩展板)
// ================================

// TJC串口屏
#define TJC_SERIAL_RX 0
#define TJC_SERIAL_TX 1
#define TJC_SERIAL_BAUD 9600

// 主副控串口通信 (通过扩展板)
#define SLAVE_COMM_RX 2              // 扩展板串口RX
#define SLAVE_COMM_TX 3              // 扩展板串口TX

// 新增OLED显示屏 (I2C)
#define OLED_SDA_PIN A4              // I2C数据线
#define OLED_SCL_PIN A5              // I2C时钟线
#define OLED_I2C_ADDRESS 0x3D        // 避免与其他I2C设备冲突

// 状态指示LED
#define STATUS_LED_POWER 5
#define STATUS_LED_COMM 6
#define STATUS_LED_ERROR 7

// 关键控制按键
#define EMERGENCY_STOP_PIN 8
#define POWER_SWITCH_PIN 9

// 模拟输入
#define BATTERY_MONITOR_PIN A0
#define TEMP_SENSOR_PIN A1

// 预留扩展接口
#define EXPANSION_PIN_1 A2
#define EXPANSION_PIN_2 A3
#define EXPANSION_PIN_3 A4
#define EXPANSION_PIN_4 A5
#define EXPANSION_PIN_5 10

// ================================
// 主副控串口通信配置 (有线连接)
// ================================
#define SLAVE_SERIAL_BAUD 115200         // 高速串口通信
#define SLAVE_SERIAL_PORT Serial1        // 使用硬件串口1 (扩展板)
#define COMM_TIMEOUT_MS 100              // 有线通信超时更短
#define HEARTBEAT_INTERVAL_MS 1000
#define MAX_RETRIES 3

// 串口通信协议
#define COMM_HEADER 0xAA
#define COMM_FOOTER 0x55
#define MAX_DATA_LENGTH 16

// 敌方通信模拟配置 (WiFi + RF24)
#define ENEMY_WIFI_SSID "Enemy_Network"
#define ENEMY_WIFI_PASSWORD "tactical123"
#define ENEMY_WIFI_CHANNEL 1             // 2412MHz
#define RECON_RF24_CHANNEL 1             // 对应2401MHz，接收WiFi信号

// ================================
// 显示配置
// ================================

// TJC触摸屏页面定义
#define PAGE_STARTUP 0
#define PAGE_MAIN_MENU 1
#define PAGE_TRAINING_SELECT 2
#define PAGE_PARAMETER_SETUP 3
#define PAGE_TRAINING_RUNNING 4
#define PAGE_DATA_ANALYSIS 5
#define PAGE_SYSTEM_SETTINGS 6
#define PAGE_ERROR_DISPLAY 7

// TJC组件ID定义
#define COMP_BTN_BASIC_TRAINING 1
#define COMP_BTN_ADVANCED_TRAINING 2
#define COMP_BTN_CUSTOM_TRAINING 3
#define COMP_BTN_SYSTEM_SETTINGS 4
#define COMP_BTN_DATA_ANALYSIS 5
#define COMP_BTN_HELP 6

// ================================
// 射频系统配置
// ================================
#define RF_MIN_FREQ 2402        // MHz
#define RF_MAX_FREQ 2461        // MHz
#define RF_DEFAULT_FREQ 2431    // MHz
#define RF_MIN_POWER 0          // dBm
#define RF_MAX_POWER 20         // dBm
#define RF_DEFAULT_POWER 10     // dBm

// VCO控制参数
#define VCO_MIN_VOLTAGE 0       // V
#define VCO_MAX_VOLTAGE 4.5     // V
#define DAC_RESOLUTION 4096     // 12-bit
#define DAC_I2C_ADDRESS 0x60

// ================================
// 训练模式定义
// ================================
#define TRAINING_MODE_NOISE 1
#define TRAINING_MODE_SWEEP 2
#define TRAINING_MODE_PULSE 3
#define TRAINING_MODE_DECEPTION 4
#define TRAINING_MODE_CUSTOM 5

// 训练参数限制
#define MIN_TRAINING_DURATION 10    // 秒
#define MAX_TRAINING_DURATION 3600  // 秒
#define DEFAULT_TRAINING_DURATION 60

#define MIN_SWEEP_RATE 1            // MHz/s
#define MAX_SWEEP_RATE 100          // MHz/s

#define MIN_PULSE_WIDTH 1           // ms
#define MAX_PULSE_WIDTH 1000        // ms

// ================================
// 系统安全配置
// ================================
#define MAX_TEMPERATURE 60          // °C
#define MIN_BATTERY_VOLTAGE 6500    // mV
#define CRITICAL_BATTERY_VOLTAGE 6000 // mV

#define TOUCH_LOCK_TIMEOUT 30000    // ms
#define AUTO_SHUTDOWN_TIMEOUT 1800000 // ms (30分钟)

// ================================
// 系统时序配置
// ================================
#define MAIN_LOOP_INTERVAL 10       // ms
#define UI_UPDATE_INTERVAL 100      // ms
#define STATUS_UPDATE_INTERVAL 1000 // ms
#define COMM_CHECK_INTERVAL 500     // ms

// ================================
// 调试配置
// ================================
#define DEBUG_MODE 1
#define SERIAL_DEBUG_BAUD 115200

#if DEBUG_MODE
#define DEBUG_PRINT(x) Serial.print(x)
#define DEBUG_PRINTLN(x) Serial.println(x)
#define DEBUG_PRINTF(fmt, ...) Serial.printf(fmt, __VA_ARGS__)
#else
#define DEBUG_PRINT(x)
#define DEBUG_PRINTLN(x)
#define DEBUG_PRINTF(fmt, ...)
#endif

// ================================
// 错误代码定义
// ================================
#define ERROR_NONE 0x0000
#define ERROR_CONFIG_INIT 0x0001
#define ERROR_SYSTEM_INIT 0x0002
#define ERROR_UI_INIT 0x0003
#define ERROR_TRAINING_INIT 0x0004
#define ERROR_COMM_INIT 0x0005
#define ERROR_SELF_TEST 0x0006

#define ERROR_COMM_TIMEOUT 0x0010
#define ERROR_COMM_CRC 0x0011
#define ERROR_COMM_LOST 0x0012

#define ERROR_RF_INIT 0x0020
#define ERROR_RF_FREQ 0x0021
#define ERROR_RF_POWER 0x0022

#define ERROR_DISPLAY_INIT 0x0030
#define ERROR_DISPLAY_COMM 0x0031

#define ERROR_BATTERY_LOW 0x0040
#define ERROR_OVER_TEMP 0x0041
#define ERROR_EMERGENCY_STOP 0x0042

// ================================
// 系统状态定义
// ================================
enum SystemState {
    SYS_INIT = 0,
    SYS_READY = 1,
    SYS_TRAINING = 2,
    SYS_ERROR = 3,
    SYS_SHUTDOWN = 4
};

enum TrainingState {
    TRAIN_IDLE = 0,
    TRAIN_SETUP = 1,
    TRAIN_RUNNING = 2,
    TRAIN_PAUSED = 3,
    TRAIN_COMPLETED = 4,
    TRAIN_ERROR = 5
};

// ================================
// 数据结构定义
// ================================
struct SystemStatus {
    SystemState state;
    unsigned long uptime;
    uint16_t batteryVoltage;    // mV
    int8_t temperature;         // °C
    uint16_t errorCode;
    bool slaveConnected;
    unsigned long timestamp;
};

struct TrainingParameters {
    uint8_t mode;               // 训练模式
    uint32_t frequency;         // 中心频率 (MHz)
    uint16_t power;             // 输出功率 (dBm)
    uint16_t bandwidth;         // 带宽 (MHz)
    uint16_t duration;          // 持续时间 (秒)
    uint16_t sweepRate;         // 扫频速率 (MHz/s)
    uint16_t pulseWidth;        // 脉冲宽度 (ms)
    uint16_t pulseInterval;     // 脉冲间隔 (ms)
};

struct TrainingData {
    uint16_t sessionId;
    unsigned long startTime;
    unsigned long duration;
    TrainingParameters params;
    uint16_t successRate;       // 成功率 (%)
    uint8_t score;              // 评分 (0-100)
    uint32_t packetsSent;
    uint32_t packetsReceived;
};

// ================================
// 内存管理配置
// ================================
#define MAX_TRAINING_SESSIONS 10
#define MAX_ERROR_LOG_ENTRIES 20
#define CONFIG_EEPROM_START 0
#define CONFIG_EEPROM_SIZE 512

// ================================
// WiFi配置 (UNO R4 WiFi) - 远程监控和控制
// ================================
#define WIFI_SSID_MAX_LENGTH 32
#define WIFI_PASSWORD_MAX_LENGTH 64
#define WIFI_TIMEOUT_MS 10000
#define WEB_SERVER_PORT 80
#define API_SERVER_PORT 8080

// WiFi功能开关
#define WIFI_REMOTE_MONITOR_ENABLED 1    // 远程监控
#define WIFI_REMOTE_CONTROL_ENABLED 1    // 远程控制
#define WIFI_DATA_UPLOAD_ENABLED 1       // 数据上传
#define WIFI_OTA_UPDATE_ENABLED 1        // 固件更新

// 默认WiFi配置
#define DEFAULT_WIFI_SSID "ECM_Training_System"
#define DEFAULT_WIFI_PASSWORD "ECM123456"
#define DEFAULT_AP_MODE true             // 默认AP模式

// ================================
// 性能监控配置
// ================================
#define PERFORMANCE_MONITOR_ENABLED 1
#define MAX_LOOP_TIME_WARNING 50    // ms
#define MEMORY_USAGE_WARNING 80     // %

// ================================
// 版本兼容性检查
// ================================
#if ARDUINO < 10800
#error "需要 Arduino IDE 1.8.0 或更高版本"
#endif

#endif // CONFIG_H
