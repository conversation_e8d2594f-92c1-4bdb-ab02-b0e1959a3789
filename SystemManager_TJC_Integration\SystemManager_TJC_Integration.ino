/*
 * SystemManager + TJC触摸屏集成程序
 * 实现干扰界面53个控件的数据绑定
 * 将传感器数据映射到TJC屏幕显示
 */

#include <DHT.h>
#include <SoftwareSerial.h>

// 传感器配置
#define DHT_PIN 8
#define DHT_TYPE DHT11
#define BATTERY_MONITOR_PIN A0

// TJC屏幕配置
#define TJC_RX_PIN 2
#define TJC_TX_PIN 3
#define TJC_BAUD_RATE 9600

// LED配置
#define STATUS_LED_POWER 5
#define STATUS_LED_COMM 6
#define STATUS_LED_ERROR 7

// 系统配置
#define MAX_TEMPERATURE 60
#define MIN_BATTERY_VOLTAGE 6500

// 全局实例
DHT dht(DHT_PIN, DHT_TYPE);
SoftwareSerial tjcSerial(TJC_RX_PIN, TJC_TX_PIN);

// 系统数据结构
struct SystemData {
    // 传感器数据
    float temperature;
    float humidity;
    uint16_t batteryVoltage;
    uint8_t batteryPercent;
    
    // 系统状态
    String systemState;
    bool systemHealthy;
    uint16_t errorCode;
    unsigned long uptime;
    
    // 干扰参数 (模拟)
    float jamFrequency;      // 干扰频率 MHz
    uint8_t jamPower;        // 干扰功率 %
    uint8_t jamMode;         // 干扰模式 0-5
    bool jamEnabled;         // 干扰开关
    
    // 目标信息 (模拟)
    float targetFreq;        // 目标频率 MHz
    int8_t targetRSSI;       // 目标信号强度 dBm
    String targetType;       // 目标类型
    bool targetDetected;     // 目标检测状态
    
    // 效果监控 (模拟)
    uint8_t jamEfficiency;   // 干扰效率 %
    float powerConsumption;  // 功耗 W
    uint16_t workingTime;    // 工作时间 min
};

SystemData sysData;
unsigned long lastTJCUpdate = 0;
unsigned long lastSensorRead = 0;
unsigned long systemStartTime = 0;

// TJC事件处理缓冲区
#define TJC_BUFFER_SIZE 64
uint8_t tjcBuffer[TJC_BUFFER_SIZE];
uint8_t tjcBufferIndex = 0;
unsigned long lastTJCEventTime = 0;

void setup() {
    Serial.begin(115200);
    Serial.println("=== SystemManager + TJC集成测试 ===");
    
    systemStartTime = millis();
    
    // 初始化硬件
    initializeHardware();
    
    // 初始化TJC屏幕
    initializeTJC();
    
    // 初始化系统数据
    initializeSystemData();
    
    Serial.println("系统初始化完成");
}

void loop() {
    // 每秒更新传感器数据
    if(millis() - lastSensorRead >= 1000) {
        lastSensorRead = millis();
        updateSensorData();
        updateSystemStatus();
    }
    
    // 每1000ms更新TJC显示 (降低频率，减少数据冲突)
    if(millis() - lastTJCUpdate >= 1000) {
        lastTJCUpdate = millis();
        updateTJCDisplay();
    }
    
    // 处理TJC触摸事件
    handleTJCEvents();
    
    // 处理串口命令
    handleSerialCommands();
    
    delay(50);
}

void initializeHardware() {
    // LED引脚
    pinMode(STATUS_LED_POWER, OUTPUT);
    pinMode(STATUS_LED_COMM, OUTPUT);
    pinMode(STATUS_LED_ERROR, OUTPUT);
    
    // DHT传感器
    dht.begin();
    delay(1000);
    
    Serial.println("硬件初始化完成");
}

void initializeTJC() {
    tjcSerial.begin(TJC_BAUD_RATE);
    delay(2000); // 等待TJC启动

    // 清空TJC串口缓冲区 (重要：清除启动时的垃圾数据)
    while(tjcSerial.available()) {
        tjcSerial.read();
    }

    // 初始化TJC缓冲区
    memset(tjcBuffer, 0, TJC_BUFFER_SIZE);
    tjcBufferIndex = 0;

    // 切换到干扰界面页面
    sendTJCCommand("page 1");
    delay(100);

    // 设置初始显示
    sendTJCText("t0", "ECM Training System");
    sendTJCText("t1", "Initializing...");

    Serial.println("TJC屏幕初始化完成");
}

void initializeSystemData() {
    sysData.temperature = 25.0;
    sysData.humidity = 50.0;
    sysData.batteryVoltage = 7400;
    sysData.batteryPercent = 75;
    sysData.systemState = "READY";
    sysData.systemHealthy = true;
    sysData.errorCode = 0;
    
    // 初始化干扰参数
    sysData.jamFrequency = 2450.0;
    sysData.jamPower = 50;
    sysData.jamMode = 1;
    sysData.jamEnabled = false;
    
    // 初始化目标信息
    sysData.targetFreq = 2400.0;
    sysData.targetRSSI = -65;
    sysData.targetType = "WiFi";
    sysData.targetDetected = false;
    
    // 初始化效果监控
    sysData.jamEfficiency = 0;
    sysData.powerConsumption = 2.5;
    sysData.workingTime = 0;
}

void updateSensorData() {
    // 读取DHT传感器
    float temp = dht.readTemperature();
    float hum = dht.readHumidity();
    
    if(!isnan(temp) && !isnan(hum)) {
        sysData.temperature = temp;
        sysData.humidity = hum;
    }
    
    // 读取电池电压
    int adcValue = analogRead(BATTERY_MONITOR_PIN);
    if(adcValue < 500) {
        sysData.batteryVoltage = 7400; // 模拟电压
    } else {
        sysData.batteryVoltage = map(adcValue, 614, 860, 6000, 8400);
        sysData.batteryVoltage = constrain(sysData.batteryVoltage, 5000, 9000);
    }
    
    sysData.batteryPercent = map(sysData.batteryVoltage, 6000, 8400, 0, 100);
    sysData.batteryPercent = constrain(sysData.batteryPercent, 0, 100);
    
    // 更新运行时间
    sysData.uptime = (millis() - systemStartTime) / 1000;
}

void updateSystemStatus() {
    // 检查系统健康状态
    bool tempOK = (sysData.temperature <= MAX_TEMPERATURE);
    bool battOK = (sysData.batteryVoltage >= MIN_BATTERY_VOLTAGE);
    
    sysData.systemHealthy = tempOK && battOK;
    
    if(!tempOK) {
        sysData.errorCode = 0x0004;
        sysData.systemState = "ERROR";
    } else if(!battOK) {
        sysData.errorCode = 0x0003;
        sysData.systemState = "ERROR";
    } else {
        sysData.errorCode = 0;
        sysData.systemState = "READY";
    }
    
    // 更新LED状态
    digitalWrite(STATUS_LED_POWER, sysData.systemHealthy ? HIGH : LOW);
    digitalWrite(STATUS_LED_ERROR, sysData.systemHealthy ? LOW : HIGH);
    
    // 模拟干扰效果数据
    if(sysData.jamEnabled) {
        sysData.jamEfficiency = random(70, 95);
        sysData.powerConsumption = 3.2 + (sysData.jamPower * 0.02);
        sysData.workingTime++;
        sysData.targetDetected = (random(0, 100) < 80); // 80%概率检测到目标
    } else {
        sysData.jamEfficiency = 0;
        sysData.powerConsumption = 1.2;
        sysData.targetDetected = (random(0, 100) < 30); // 30%概率检测到目标
    }
}

void updateTJCDisplay() {
    // 更新系统信息区域 (控件1-10)
    sendTJCText("t2", "Temp: " + String(sysData.temperature, 1) + "C");
    sendTJCText("t3", "Humidity: " + String(sysData.humidity, 0) + "%");
    sendTJCText("t4", "Battery: " + String(sysData.batteryPercent) + "%");
    sendTJCText("t5", "State: " + sysData.systemState);
    sendTJCText("t6", "Uptime: " + formatUptime(sysData.uptime));
    
    // 更新目标信息区域 (控件11-20)
    sendTJCText("t7", "Target: " + sysData.targetType);
    sendTJCText("t8", "Freq: " + String(sysData.targetFreq, 1) + "MHz");
    sendTJCText("t9", "RSSI: " + String(sysData.targetRSSI) + "dBm");
    sendTJCText("t10", sysData.targetDetected ? "DETECTED" : "SCANNING");
    
    // 更新干扰参数区域 (控件21-35)
    sendTJCText("t11", "Jam Freq: " + String(sysData.jamFrequency, 1) + "MHz");
    sendTJCNumber("n0", sysData.jamPower);
    sendTJCNumber("n1", sysData.jamMode);
    sendTJCText("t12", sysData.jamEnabled ? "JAMMING" : "STANDBY");
    
    // 更新滑块控件 (控件36-45)
    sendTJCSlider("h0", sysData.jamPower);
    sendTJCSlider("h1", map(sysData.jamFrequency, 2400, 2500, 0, 100));
    
    // 更新效果监控区域 (控件46-53)
    sendTJCText("t13", "Efficiency: " + String(sysData.jamEfficiency) + "%");
    sendTJCText("t14", "Power: " + String(sysData.powerConsumption, 1) + "W");
    sendTJCText("t15", "Time: " + String(sysData.workingTime) + "min");
    sendTJCProgress("j0", sysData.jamEfficiency);
    sendTJCProgress("j1", sysData.batteryPercent);
}

void sendTJCCommand(String command) {
    tjcSerial.print(command);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);

    // 添加小延迟，防止命令发送过快
    delay(10);
}

void sendTJCText(String control, String text) {
    String command = control + ".txt=\"" + text + "\"";
    sendTJCCommand(command);
}

void sendTJCNumber(String control, int value) {
    String command = control + ".val=" + String(value);
    sendTJCCommand(command);
}

void sendTJCSlider(String control, int value) {
    value = constrain(value, 0, 100);
    String command = control + ".val=" + String(value);
    sendTJCCommand(command);
}

void sendTJCProgress(String control, int value) {
    value = constrain(value, 0, 100);
    String command = control + ".val=" + String(value);
    sendTJCCommand(command);
}

String formatUptime(unsigned long seconds) {
    unsigned long hours = seconds / 3600;
    unsigned long minutes = (seconds % 3600) / 60;
    unsigned long secs = seconds % 60;
    
    String result = "";
    if(hours < 10) result += "0";
    result += String(hours) + ":";
    if(minutes < 10) result += "0";
    result += String(minutes) + ":";
    if(secs < 10) result += "0";
    result += String(secs);
    
    return result;
}

void handleTJCEvents() {
    if(tjcSerial.available()) {
        Serial.print("TJC返回数据: ");

        String response = "";
        bool hasValidData = false;

        // 正确的数据处理方式 (参考TJC_Basic_Test)
        while(tjcSerial.available()) {
            char c = tjcSerial.read();
            if(c == 0xFF) {
                Serial.print("[FF]");
            } else if(c >= 32 && c <= 126) {
                // 只处理可打印的ASCII字符
                Serial.print(c);
                response += c;
                hasValidData = true;
            } else {
                // 显示不可打印字符的十六进制值
                Serial.print("[");
                Serial.print((int)c, HEX);
                Serial.print("]");
            }
        }
        Serial.println();

        // 解析有效的触摸事件
        if(hasValidData && response.length() > 0) {
            parseTJCTouchEvent(response);
        }
    }
}

void parseTJCTouchEvent(String data) {
    Serial.print("解析触摸事件: ");
    Serial.println(data);

    // 解析触摸事件 (参考TJC协议)
    if(data.indexOf("65 00") >= 0) {
        Serial.println("检测到按钮按下事件");
        handleTJCButtonPress(data);
    } else if(data.indexOf("65 01") >= 0) {
        Serial.println("检测到按钮释放事件");
        handleTJCButtonRelease(data);
    } else if(data.indexOf("b0") >= 0) {
        // 干扰开关按钮 (简化版)
        sysData.jamEnabled = !sysData.jamEnabled;
        Serial.println("切换干扰状态: " + String(sysData.jamEnabled ? "开启" : "关闭"));
    } else {
        Serial.println("未识别的触摸事件");
    }
}

void handleTJCButtonPress(String data) {
    Serial.println("处理按钮按下");
    // 可以根据具体的按钮ID进行不同处理
    // 这里添加具体的按钮响应逻辑
}

void handleTJCButtonRelease(String data) {
    Serial.println("处理按钮释放");
    // 按钮释放后的处理逻辑
}

void handleSerialCommands() {
    if(Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if(command == "status") {
            printSystemStatus();
        } else if(command == "jam_on") {
            sysData.jamEnabled = true;
            Serial.println("干扰开启");
        } else if(command == "jam_off") {
            sysData.jamEnabled = false;
            Serial.println("干扰关闭");
        } else if(command.startsWith("power:")) {
            int power = command.substring(6).toInt();
            sysData.jamPower = constrain(power, 0, 100);
            Serial.println("设置干扰功率: " + String(sysData.jamPower) + "%");
        } else if(command.startsWith("freq:")) {
            float freq = command.substring(5).toFloat();
            sysData.jamFrequency = constrain(freq, 2400.0, 2500.0);
            Serial.println("设置干扰频率: " + String(sysData.jamFrequency) + "MHz");
        }
    }
}

void printSystemStatus() {
    Serial.println("=== 系统状态 ===");
    Serial.println("传感器数据:");
    Serial.println("  温度: " + String(sysData.temperature) + "°C");
    Serial.println("  湿度: " + String(sysData.humidity) + "%");
    Serial.println("  电池: " + String(sysData.batteryVoltage) + "mV (" + String(sysData.batteryPercent) + "%)");
    Serial.println("系统状态:");
    Serial.println("  状态: " + sysData.systemState);
    Serial.println("  健康: " + String(sysData.systemHealthy ? "正常" : "异常"));
    Serial.println("  运行时间: " + formatUptime(sysData.uptime));
    Serial.println("干扰参数:");
    Serial.println("  频率: " + String(sysData.jamFrequency) + "MHz");
    Serial.println("  功率: " + String(sysData.jamPower) + "%");
    Serial.println("  状态: " + String(sysData.jamEnabled ? "开启" : "关闭"));
    Serial.println("  效率: " + String(sysData.jamEfficiency) + "%");
    Serial.println("===============");
}
