2 /*
 * UIManager测试程序
 * 用于验证UIManager的基本功能
 * 
 * 测试内容:
 * 1. TJC串口通信
 * 2. 滑块事件处理
 * 3. 页面状态管理
 * 4. 界面内容更新
 */

#include <SoftwareSerial.h>

// 模拟TJC串口
SoftwareSerial tjcSerial(2, 3); // RX, TX

// 调试宏
#define DEBUG_PRINT(x) Serial.print(x)
#define DEBUG_PRINTLN(x) Serial.println(x)

// 页面类型枚举
enum PageType {
    PAGE_STARTUP = 0,
    PAGE_MAIN_MENU = 1,
    PAGE_SIGNAL_RECON = 2,
    PAGE_JAMMING_TRAINING = 3,
    PAGE_DATA_ANALYSIS = 4,
    PAGE_SYSTEM_SETTINGS = 5,
    PAGE_ERROR_DISPLAY = 6
};

// 全局变量
PageType currentPage = PAGE_STARTUP;
unsigned long lastUpdate = 0;
bool autoJumpCompleted = false;  // 添加自动跳转标志

void setup() {
    Serial.begin(115200);
    tjcSerial.begin(9600);

    Serial.println(F("=== UIManager功能测试 ==="));
    Serial.println(F("1. 测试TJC通信"));
    Serial.println(F("2. 测试滑块事件"));
    Serial.println(F("3. 测试页面管理"));
    Serial.println(F("========================"));

    Serial.println(F("等待TJC屏幕完全启动..."));
    // 不在setup中立即跳转，等待TJC完全启动
}

void loop() {
    // 延迟自动跳转到主菜单 (解决启动同步问题)
    if(!autoJumpCompleted && millis() > 3000) {  // 3秒后开始启动流程
        Serial.println(F("开始启动流程..."));

        // 显示启动进度条
        showStartupProgress();

        // 执行初始化测试
        testTJCCommunication();
        delay(500);

        // 跳转到主菜单
        showMainMenu();
        autoJumpCompleted = true;
        Serial.println(F("启动流程完成"));
    }

    // 处理滑块事件
    handleSliderEvents();

    // 定期更新界面
    if(millis() - lastUpdate >= 2000) {
        lastUpdate = millis();
        updateCurrentPageContent();
    }

    // 处理串口命令
    handleSerialCommands();
}

// 显示启动进度条
void showStartupProgress() {
    Serial.println(F("显示启动进度条..."));

    // 确保在startup页面
    sendTJCCommand("page startup");
    delay(200);

    // 更新启动信息
    sendTJCText("startup.t0", "电子对抗模拟训练系统");
    sendTJCText("startup.t1", "系统正在初始化...");
    delay(200);

    // 模拟初始化进度
    for(int progress = 0; progress <= 100; progress += 20) {
        sendTJCValue("startup.j0", progress);
        sendTJCText("startup.t2", "初始化进度: " + String(progress) + "%");

        Serial.print(F("初始化进度: "));
        Serial.print(progress);
        Serial.println(F("%"));

        delay(300);  // 每步延迟300ms
    }

    // 初始化完成
    sendTJCText("startup.t1", "初始化完成，正在启动...");
    sendTJCText("startup.t2", "系统就绪");
    delay(500);

    Serial.println(F("启动进度条完成"));
}

// 测试TJC通信
void testTJCCommunication() {
    Serial.println(F("测试TJC通信..."));

    // 测试页面跳转
    sendTJCCommand("page main");
    delay(100);

    // 测试文本更新
    sendTJCText("main.t0", "电子对抗模拟训练系统");
    delay(100);

    // 测试数值更新
    sendTJCValue("main.j1", 85);
    delay(100);

    Serial.println(F("TJC通信测试完成"));
}

// 发送TJC命令
void sendTJCCommand(const String& command) {
    tjcSerial.print(command);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    
    Serial.print(F("发送TJC命令: "));
    Serial.println(command);
}

// 发送文本到TJC
void sendTJCText(const String& component, const String& text) {
    String command = component + ".txt=\"" + text + "\"";
    sendTJCCommand(command);
}

// 发送数值到TJC
void sendTJCValue(const String& component, int value) {
    String command = component + ".val=" + String(value);
    sendTJCCommand(command);
}

// 显示主菜单
void showMainMenu() {
    currentPage = PAGE_MAIN_MENU;
    sendTJCCommand("page main");
    
    // 更新主菜单内容
    sendTJCText("main.t0", "电子对抗模拟训练系统");
    sendTJCText("main.t1", "[READY] 系统就绪 | 温度: 28°C");
    sendTJCText("main.t2", "85%");
    sendTJCValue("main.j1", 85);
    
    Serial.println(F("显示主菜单"));
}

// 处理滑块事件 (优化响应性)
void handleSliderEvents() {
    // 处理所有可用数据，避免数据积压
    while(tjcSerial.available() > 0) {
        if(tjcSerial.available() >= 4) {
            uint8_t buffer[4];
            tjcSerial.readBytes(buffer, 4);

            // 打印原始数据用于调试
            Serial.print(F("接收数据: "));
            for(int i = 0; i < 4; i++) {
                Serial.print(buffer[i], HEX);
                Serial.print(F(" "));
            }
            Serial.println();

            if(buffer[0] == 0x55 && buffer[1] == 0xAA) {
                uint8_t sliderType = buffer[2];
                uint8_t sliderValue = buffer[3];

                Serial.print(F(">>> 滑块事件: 类型="));
                Serial.print(sliderType);
                Serial.print(F(", 值="));
                Serial.println(sliderValue);

                processSliderInput(sliderType, sliderValue);
            } else {
                Serial.println(F(">>> 无效数据包"));
            }
        } else {
            // 数据不足，等待或清除
            delay(10);
            if(tjcSerial.available() < 4) {
                Serial.println(F(">>> 清除不完整数据"));
                while(tjcSerial.available() > 0) {
                    Serial.print(tjcSerial.read(), HEX);
                    Serial.print(F(" "));
                }
                Serial.println();
                break;
            }
        }
    }
}

// 处理滑块输入 (根据您的反馈修正)
void processSliderInput(uint8_t sliderType, uint8_t sliderValue) {
    switch(sliderType) {
        case 0x01: // 扫描速度滑块 (直接显示，您的TJC配置是0-100)
            Serial.print(F(">>> 扫描速度调节: "));
            Serial.println(sliderValue);
            sendTJCText("recon.t29", String(sliderValue) + "%");
            break;

        case 0x02: // 功率滑块 (直接显示，您的TJC配置是0-20)
            Serial.print(F(">>> 功率调节: "));
            Serial.println(sliderValue);
            sendTJCText("jamming.t38", String(sliderValue) + "dBm");
            break;

        case 0x03: // 频率滑块 (需要转换，TJC输出0-255)
            {
                uint16_t frequency = map(sliderValue, 0, 255, 2402, 2461);
                Serial.print(F(">>> 频率调节: "));
                Serial.print(sliderValue);
                Serial.print(F(" -> "));
                Serial.println(frequency);
                sendTJCText("jamming.t36", String(frequency) + "MHz");
            }
            break;

        case 0x04: // 带宽滑块 (直接显示，您的TJC配置是5-40)
            Serial.print(F(">>> 带宽调节: "));
            Serial.println(sliderValue);
            sendTJCText("jamming.t40", String(sliderValue) + "MHz");
            break;

        default:
            Serial.print(F(">>> 未知滑块类型: "));
            Serial.println(sliderType);
            break;
    }
}

// 更新当前页面内容
void updateCurrentPageContent() {
    switch(currentPage) {
        case PAGE_MAIN_MENU:
            updateMainMenuStatus();
            break;
        case PAGE_SIGNAL_RECON:
            updateReconPageContent();
            break;
        case PAGE_JAMMING_TRAINING:
            updateJammingPageContent();
            break;
        default:
            break;
    }
}

// 更新主菜单状态
void updateMainMenuStatus() {
    String statusText = "[READY] 系统就绪 | 温度: " + String(random(25, 35)) + "°C";
    sendTJCText("main.t1", statusText);
    
    uint8_t batteryPercent = random(80, 100);
    sendTJCText("main.t2", String(batteryPercent) + "%");
    sendTJCValue("main.j1", batteryPercent);
    
    Serial.println(F("主菜单状态已更新"));
}

// 更新侦察页面内容
void updateReconPageContent() {
    String detectionResult = "[SCANNING] 正在扫描...\r\n当前频率: " + String(random(2402, 2461)) + " MHz\r\n检测到: " + String(random(0, 3)) + " 个信号";
    sendTJCText("recon.t27", detectionResult);
    
    Serial.println(F("侦察页面内容已更新"));
}

// 更新干扰页面内容
void updateJammingPageContent() {
    String effectText = "[EFFECT] 干扰效果监控\r\n中断率: " + String(random(70, 95)) + "%  丢包率: " + String(random(60, 90)) + "%\r\n信号衰减: " + String(random(15, 30)) + "dB  干扰效率: " + String(random(80, 98)) + "%";
    sendTJCText("jamming.t45", effectText);
    
    Serial.println(F("干扰页面内容已更新"));
}

// 处理串口命令
void handleSerialCommands() {
    if(Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if(command == "main") {
            showMainMenu();
        } else if(command == "recon") {
            currentPage = PAGE_SIGNAL_RECON;
            sendTJCCommand("page recon");
            Serial.println(F("切换到侦察页面"));
        } else if(command == "jamming") {
            currentPage = PAGE_JAMMING_TRAINING;
            sendTJCCommand("page jamming");
            Serial.println(F("切换到干扰页面"));
        } else if(command == "test") {
            testTJCCommunication();
        } else if(command == "help") {
            Serial.println(F("可用命令: main, recon, jamming, test, help"));
        } else {
            Serial.println(F("未知命令，输入help查看帮助"));
        }
    }
}
