/*
 * 主副控串口通信测试程序
 * Arduino UNO R4 WiFi 主控端
 * 
 * 测试目标:
 * 1. 验证115200波特率串口通信
 * 2. 测试指令发送和状态接收
 * 3. 验证数据包格式和校验
 * 4. 测试通信稳定性和错误处理
 */

// ==================== 通信协议配置 ====================
#define SLAVE_SERIAL Serial1    // 使用硬件串口1与副控通信
#define SLAVE_BAUD_RATE 115200  // 通信波特率

// 通信协议定义
#define COMM_START_BYTE 0xAA
#define COMM_END_BYTE 0x55
#define COMM_MAX_DATA_LEN 16

// 指令类型定义 (与副控保持一致)
enum MasterCommand {
    CMD_SET_FREQUENCY = 0x01,    // 设置干扰频率
    CMD_SET_POWER = 0x02,        // 设置干扰功率
    CMD_START_JAMMING = 0x03,    // 开始干扰
    CMD_STOP_JAMMING = 0x04,     // 停止干扰
    CMD_START_SCAN = 0x05,       // 开始侦察
    CMD_STOP_SCAN = 0x06,        // 停止侦察
    CMD_GET_STATUS = 0x07,       // 获取状态
    CMD_RESET = 0x08             // 重置副控
};

// 状态反馈类型 (与副控保持一致)
enum SlaveStatus {
    STATUS_READY = 0x01,         // 就绪
    STATUS_JAMMING = 0x02,       // 干扰中
    STATUS_SCANNING = 0x03,      // 侦察中
    STATUS_ERROR = 0x04          // 错误
};

// ==================== 测试统计 ====================
struct TestStats {
    unsigned long totalSent;      // 总发送指令数
    unsigned long totalReceived;  // 总接收响应数
    unsigned long successCount;   // 成功通信次数
    unsigned long errorCount;     // 通信错误次数
    unsigned long timeoutCount;   // 超时次数
    float successRate;            // 成功率
    unsigned long lastTestTime;   // 最后测试时间
} testStats;

// ==================== 函数声明 ====================
void initializeTest();
void runCommunicationTest();
void sendCommandToSlave(uint8_t command, uint8_t* data = nullptr, uint8_t dataLen = 0);
bool waitForSlaveResponse(unsigned long timeoutMs = 2000);
void parseSlaveResponse();
uint8_t calculateChecksum(uint8_t* data, uint8_t length);
void printTestResults();
void runInteractiveTest();

void setup() {
    Serial.begin(115200);
    Serial.println(F("=== 主副控串口通信测试程序 ==="));
    Serial.println(F("Arduino UNO R4 WiFi 主控端"));
    Serial.println();
    
    // 初始化测试
    initializeTest();
    
    Serial.println(F("测试程序启动完成"));
    Serial.println(F("输入命令进行测试:"));
    Serial.println(F("  auto    - 自动测试所有指令"));
    Serial.println(F("  status  - 查询副控状态"));
    Serial.println(F("  freq    - 设置频率测试"));
    Serial.println(F("  power   - 设置功率测试"));
    Serial.println(F("  jam     - 干扰控制测试"));
    Serial.println(F("  scan    - 侦察控制测试"));
    Serial.println(F("  reset   - 重置副控测试"));
    Serial.println(F("  stats   - 显示测试统计"));
    Serial.println(F("  help    - 显示帮助"));
    Serial.println();
}

void loop() {
    // 处理串口命令
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if (command == "auto") {
            runCommunicationTest();
        } else if (command == "status") {
            Serial.println(F("发送状态查询指令..."));
            sendCommandToSlave(CMD_GET_STATUS);
        } else if (command == "freq") {
            Serial.println(F("发送频率设置指令 (2440.0MHz)..."));
            float frequency = 2440.0;
            sendCommandToSlave(CMD_SET_FREQUENCY, (uint8_t*)&frequency, 4);
        } else if (command == "power") {
            Serial.println(F("发送功率设置指令 (128)..."));
            uint8_t power = 128;
            sendCommandToSlave(CMD_SET_POWER, &power, 1);
        } else if (command == "jam") {
            Serial.println(F("发送干扰控制指令..."));
            sendCommandToSlave(CMD_START_JAMMING);
            delay(2000);
            sendCommandToSlave(CMD_STOP_JAMMING);
        } else if (command == "scan") {
            Serial.println(F("发送侦察控制指令..."));
            sendCommandToSlave(CMD_START_SCAN);
            delay(2000);
            sendCommandToSlave(CMD_STOP_SCAN);
        } else if (command == "reset") {
            Serial.println(F("发送重置指令..."));
            sendCommandToSlave(CMD_RESET);
        } else if (command == "stats") {
            printTestResults();
        } else if (command == "help") {
            Serial.println(F("可用命令: auto, status, freq, power, jam, scan, reset, stats, help"));
        } else {
            Serial.println(F("未知命令，输入help查看帮助"));
        }
    }
    
    // 检查副控响应
    if (SLAVE_SERIAL.available()) {
        parseSlaveResponse();
    }
    
    delay(10);
}

// ==================== 测试函数实现 ====================
void initializeTest() {
    // 初始化串口通信
    SLAVE_SERIAL.begin(SLAVE_BAUD_RATE);
    
    // 清空串口缓冲区
    while(SLAVE_SERIAL.available()) {
        SLAVE_SERIAL.read();
    }
    
    // 初始化测试统计
    testStats.totalSent = 0;
    testStats.totalReceived = 0;
    testStats.successCount = 0;
    testStats.errorCount = 0;
    testStats.timeoutCount = 0;
    testStats.successRate = 0.0;
    testStats.lastTestTime = millis();
    
    Serial.println(F("串口通信初始化完成 (115200波特率)"));
}

void runCommunicationTest() {
    Serial.println(F("=== 开始自动通信测试 ==="));
    
    // 测试1: 状态查询
    Serial.println(F("测试1: 状态查询"));
    sendCommandToSlave(CMD_GET_STATUS);
    delay(1000);
    
    // 测试2: 频率设置
    Serial.println(F("测试2: 频率设置"));
    float testFreq = 2440.0;
    sendCommandToSlave(CMD_SET_FREQUENCY, (uint8_t*)&testFreq, 4);
    delay(1000);
    
    // 测试3: 功率设置
    Serial.println(F("测试3: 功率设置"));
    uint8_t testPower = 100;
    sendCommandToSlave(CMD_SET_POWER, &testPower, 1);
    delay(1000);
    
    // 测试4: 侦察控制
    Serial.println(F("测试4: 侦察控制"));
    sendCommandToSlave(CMD_START_SCAN);
    delay(2000);
    sendCommandToSlave(CMD_STOP_SCAN);
    delay(1000);
    
    // 测试5: 干扰控制
    Serial.println(F("测试5: 干扰控制"));
    sendCommandToSlave(CMD_START_JAMMING);
    delay(2000);
    sendCommandToSlave(CMD_STOP_JAMMING);
    delay(1000);
    
    // 测试6: 重置
    Serial.println(F("测试6: 重置测试"));
    sendCommandToSlave(CMD_RESET);
    delay(1000);
    
    Serial.println(F("=== 自动测试完成 ==="));
    printTestResults();
}

void sendCommandToSlave(uint8_t command, uint8_t* data, uint8_t dataLen) {
    if (dataLen > COMM_MAX_DATA_LEN) {
        Serial.println(F("数据长度超出限制"));
        return;
    }
    
    // 构建数据包
    uint8_t packet[6 + dataLen];
    packet[0] = COMM_START_BYTE;
    packet[1] = command;
    packet[2] = dataLen;
    
    // 复制数据
    for (uint8_t i = 0; i < dataLen; i++) {
        packet[3 + i] = data[i];
    }
    
    // 计算校验和
    uint8_t checksumData[2 + dataLen];
    checksumData[0] = packet[1];
    checksumData[1] = packet[2];
    for (uint8_t i = 0; i < dataLen; i++) {
        checksumData[2 + i] = packet[3 + i];
    }
    packet[3 + dataLen] = calculateChecksum(checksumData, 2 + dataLen);
    packet[4 + dataLen] = COMM_END_BYTE;
    
    // 发送数据包
    SLAVE_SERIAL.write(packet, 5 + dataLen);
    testStats.totalSent++;
    
    Serial.print(F("发送指令: 0x"));
    Serial.print(command, HEX);
    Serial.print(F(", 数据长度: "));
    Serial.print(dataLen);
    Serial.print(F(", 校验和: 0x"));
    Serial.println(packet[3 + dataLen], HEX);
    
    // 等待响应
    if (waitForSlaveResponse()) {
        testStats.successCount++;
    } else {
        testStats.timeoutCount++;
    }
    
    // 更新成功率
    if (testStats.totalSent > 0) {
        testStats.successRate = (float)testStats.successCount / testStats.totalSent * 100.0;
    }
}

bool waitForSlaveResponse(unsigned long timeoutMs) {
    unsigned long startTime = millis();
    
    while (millis() - startTime < timeoutMs) {
        if (SLAVE_SERIAL.available()) {
            return true;
        }
        delay(10);
    }
    
    Serial.println(F("等待副控响应超时"));
    return false;
}

void parseSlaveResponse() {
    static uint8_t buffer[32];
    static uint8_t bufferIndex = 0;
    
    while (SLAVE_SERIAL.available()) {
        uint8_t byte = SLAVE_SERIAL.read();
        
        if (bufferIndex == 0 && byte == COMM_START_BYTE) {
            buffer[bufferIndex++] = byte;
        } else if (bufferIndex > 0) {
            buffer[bufferIndex++] = byte;
            
            // 检查是否接收完整数据包
            if (bufferIndex >= 5 && byte == COMM_END_BYTE) {
                // 解析数据包
                uint8_t status = buffer[1];
                uint8_t dataLength = buffer[2];
                uint8_t checksum = buffer[3 + dataLength];
                
                // 验证校验和
                uint8_t calculatedChecksum = calculateChecksum(&buffer[1], 2 + dataLength);
                if (checksum == calculatedChecksum) {
                    testStats.totalReceived++;
                    
                    Serial.print(F("收到副控响应: 状态=0x"));
                    Serial.print(status, HEX);
                    Serial.print(F(" ("));
                    switch(status) {
                        case STATUS_READY: Serial.print(F("就绪")); break;
                        case STATUS_JAMMING: Serial.print(F("干扰中")); break;
                        case STATUS_SCANNING: Serial.print(F("侦察中")); break;
                        case STATUS_ERROR: Serial.print(F("错误")); break;
                        default: Serial.print(F("未知")); break;
                    }
                    Serial.print(F("), 数据长度="));
                    Serial.println(dataLength);
                    
                    if (dataLength > 0) {
                        Serial.print(F("数据: "));
                        for (uint8_t i = 0; i < dataLength; i++) {
                            Serial.print(F("0x"));
                            Serial.print(buffer[3 + i], HEX);
                            Serial.print(F(" "));
                        }
                        Serial.println();
                    }
                } else {
                    Serial.println(F("副控响应校验和错误"));
                    testStats.errorCount++;
                }
                
                bufferIndex = 0;
            } else if (bufferIndex >= 32) {
                // 缓冲区溢出，重置
                bufferIndex = 0;
            }
        }
    }
}

uint8_t calculateChecksum(uint8_t* data, uint8_t length) {
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

void printTestResults() {
    Serial.println(F("=== 通信测试统计 ==="));
    Serial.print(F("总发送指令: "));
    Serial.println(testStats.totalSent);
    Serial.print(F("总接收响应: "));
    Serial.println(testStats.totalReceived);
    Serial.print(F("成功通信: "));
    Serial.println(testStats.successCount);
    Serial.print(F("通信错误: "));
    Serial.println(testStats.errorCount);
    Serial.print(F("响应超时: "));
    Serial.println(testStats.timeoutCount);
    Serial.print(F("通信成功率: "));
    Serial.print(testStats.successRate, 1);
    Serial.println(F("%"));
    Serial.print(F("测试运行时间: "));
    Serial.print((millis() - testStats.lastTestTime) / 1000);
    Serial.println(F("秒"));
    Serial.println(F("=================="));
}
