/*
 * 主控RF24通信测试程序
 * Arduino UNO R4 WiFi 主控端
 * 
 * 测试目标:
 * 1. 验证NRF24L01硬件连接
 * 2. 测试2440MHz通信发送
 * 3. 验证数据包发送成功率
 * 4. 模拟WiFi数据源通信
 * 5. 为示波器测试提供RF信号
 */

#include <SPI.h>
#include <RF24.h>

// ==================== NRF24L01配置 ====================
#define COMM_RF_CE_PIN 9      // NRF24L01 CE引脚
#define COMM_RF_CSN_PIN 10    // NRF24L01 CSN引脚
#define COMM_RF_CHANNEL 40    // 2440MHz通信频道
#define COMM_RF_ADDRESS "ENEMY"  // 通信地址

// ==================== 全局实例 ====================
RF24 commRadio(COMM_RF_CE_PIN, COMM_RF_CSN_PIN);

// ==================== 通信数据结构 ====================
struct CommPacketData {
    unsigned long timestamp;
    uint16_t sequenceNumber;
    char wifiData[20];
    uint8_t checksum;
};

// ==================== 测试统计 ====================
struct TestStats {
    unsigned long totalPackets;   // 总发送包数
    unsigned long successPackets; // 成功发送包数
    unsigned long failedPackets;  // 发送失败包数
    float successRate;            // 发送成功率
    unsigned long startTime;      // 测试开始时间
    unsigned long lastPacketTime; // 最后发包时间
    bool continuousMode;          // 连续发送模式
    uint16_t packetInterval;      // 发包间隔 (ms)
} testStats;

// ==================== 函数声明 ====================
void initializeRF24();
void runRF24Test();
void sendTestPacket();
void sendContinuousPackets();
void printTestStats();
void printRF24Status();

void setup() {
    Serial.begin(115200);
    Serial.println(F("=== 主控RF24通信测试程序 ==="));
    Serial.println(F("Arduino UNO R4 WiFi 主控端"));
    Serial.println(F("测试频率: 2440MHz (频道40)"));
    Serial.println();
    
    // 初始化RF24
    initializeRF24();
    
    Serial.println(F("RF24测试程序启动完成"));
    Serial.println(F("输入命令进行测试:"));
    Serial.println(F("  single  - 发送单个测试包"));
    Serial.println(F("  burst   - 发送10个连续包"));
    Serial.println(F("  start   - 开始连续发送模式"));
    Serial.println(F("  stop    - 停止连续发送模式"));
    Serial.println(F("  fast    - 快速发送 (100ms间隔)"));
    Serial.println(F("  slow    - 慢速发送 (1000ms间隔)"));
    Serial.println(F("  status  - 显示RF24状态"));
    Serial.println(F("  stats   - 显示测试统计"));
    Serial.println(F("  reset   - 重置测试统计"));
    Serial.println(F("  help    - 显示帮助"));
    Serial.println();
    Serial.println(F("注意: 连续发送模式用于示波器测试RF信号"));
    Serial.println();
}

void loop() {
    // 处理串口命令
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if (command == "single") {
            Serial.println(F("发送单个测试包..."));
            sendTestPacket();
        } else if (command == "burst") {
            Serial.println(F("发送10个连续包..."));
            for (int i = 0; i < 10; i++) {
                sendTestPacket();
                delay(100);
            }
        } else if (command == "start") {
            Serial.println(F("开始连续发送模式..."));
            testStats.continuousMode = true;
        } else if (command == "stop") {
            Serial.println(F("停止连续发送模式"));
            testStats.continuousMode = false;
        } else if (command == "fast") {
            testStats.packetInterval = 100;
            Serial.println(F("设置快速发送模式 (100ms间隔)"));
        } else if (command == "slow") {
            testStats.packetInterval = 1000;
            Serial.println(F("设置慢速发送模式 (1000ms间隔)"));
        } else if (command == "status") {
            printRF24Status();
        } else if (command == "stats") {
            printTestStats();
        } else if (command == "reset") {
            // 重置测试统计
            testStats.totalPackets = 0;
            testStats.successPackets = 0;
            testStats.failedPackets = 0;
            testStats.successRate = 0.0;
            testStats.startTime = millis();
            Serial.println(F("测试统计已重置"));
        } else if (command == "help") {
            Serial.println(F("可用命令: single, burst, start, stop, fast, slow, status, stats, reset, help"));
            Serial.println(F("诊断命令: debug, power, spi, reset_rf"));
        } else if (command == "debug") {
            Serial.println(F("执行详细诊断..."));
            performDetailedDiagnostics();
        } else if (command == "power") {
            Serial.println(F("检查电源供电..."));
            checkPowerSupply();
        } else if (command == "spi") {
            Serial.println(F("测试SPI通信..."));
            testSPICommunication();
        } else if (command == "reset_rf") {
            Serial.println(F("重置NRF24L01..."));
            initializeRF24();
        } else {
            Serial.println(F("未知命令，输入help查看帮助"));
        }
    }
    
    // 连续发送模式
    if (testStats.continuousMode) {
        sendContinuousPackets();
    }
    
    delay(10);
}

// ==================== RF24函数实现 ====================
void initializeRF24() {
    Serial.println(F("初始化NRF24L01..."));
    
    if (!commRadio.begin()) {
        Serial.println(F("❌ NRF24L01初始化失败"));
        Serial.println(F("请检查硬件连接:"));
        Serial.println(F("  CE  -> Pin 9"));
        Serial.println(F("  CSN -> Pin 10"));
        Serial.println(F("  MOSI -> Pin 11"));
        Serial.println(F("  MISO -> Pin 12"));
        Serial.println(F("  SCK -> Pin 13"));
        Serial.println(F("  VCC -> 3.3V"));
        Serial.println(F("  GND -> GND"));
        return;
    }
    
    // 配置RF24参数
    commRadio.setPALevel(RF24_PA_HIGH);     // 高功率
    commRadio.setDataRate(RF24_250KBPS);    // 低速率，高可靠性
    commRadio.setChannel(COMM_RF_CHANNEL);  // 2440MHz
    commRadio.setAutoAck(true);             // 开启自动应答
    commRadio.enableAckPayload();           // 启用应答负载
    commRadio.setRetries(3, 5);             // 重传设置
    
    // 设置通信地址
    uint8_t address[6] = COMM_RF_ADDRESS;
    commRadio.openWritingPipe(address);
    commRadio.openReadingPipe(1, address);
    
    commRadio.stopListening(); // 设置为发送模式
    
    // 初始化测试统计
    testStats.totalPackets = 0;
    testStats.successPackets = 0;
    testStats.failedPackets = 0;
    testStats.successRate = 0.0;
    testStats.startTime = millis();
    testStats.lastPacketTime = 0;
    testStats.continuousMode = false;
    testStats.packetInterval = 500; // 默认500ms间隔
    
    Serial.println(F("✅ NRF24L01初始化成功"));
    printRF24Status();
}

void sendTestPacket() {
    // 构造测试数据包
    CommPacketData packet;
    packet.timestamp = millis();
    packet.sequenceNumber = testStats.totalPackets;
    snprintf(packet.wifiData, sizeof(packet.wifiData), "WiFi_Data_%04d", packet.sequenceNumber);
    packet.checksum = packet.timestamp ^ packet.sequenceNumber;
    
    // 发送数据包
    bool result = commRadio.write(&packet, sizeof(packet));
    
    testStats.totalPackets++;
    testStats.lastPacketTime = millis();
    
    if (result) {
        testStats.successPackets++;
        Serial.print(F("✅ 包#"));
        Serial.print(packet.sequenceNumber);
        Serial.print(F(" 发送成功 ("));
        Serial.print(sizeof(packet));
        Serial.println(F("字节)"));
    } else {
        testStats.failedPackets++;
        Serial.print(F("❌ 包#"));
        Serial.print(packet.sequenceNumber);
        Serial.println(F(" 发送失败"));
    }
    
    // 更新成功率
    if (testStats.totalPackets > 0) {
        testStats.successRate = (float)testStats.successPackets / testStats.totalPackets * 100.0;
    }
}

void sendContinuousPackets() {
    static unsigned long lastSendTime = 0;
    
    if (millis() - lastSendTime >= testStats.packetInterval) {
        sendTestPacket();
        lastSendTime = millis();
        
        // 每100个包显示一次统计
        if (testStats.totalPackets % 100 == 0) {
            Serial.print(F("📊 已发送 "));
            Serial.print(testStats.totalPackets);
            Serial.print(F(" 包，成功率: "));
            Serial.print(testStats.successRate, 1);
            Serial.println(F("%"));
        }
    }
}

void printRF24Status() {
    Serial.println(F("=== NRF24L01详细状态 ==="));

    // 基本连接状态
    Serial.print(F("芯片连接: "));
    Serial.println(commRadio.isChipConnected() ? F("✅ 正常") : F("❌ 异常"));

    // 详细寄存器状态
    Serial.println(F("\n--- 寄存器状态 ---"));
    uint8_t config = commRadio.read_register(0x00);
    Serial.print(F("CONFIG (0x00): 0x"));
    Serial.println(config, HEX);
    Serial.print(F("  PWR_UP: "));
    Serial.println((config & 0x02) ? F("开启") : F("关闭"));
    Serial.print(F("  PRIM_RX: "));
    Serial.println((config & 0x01) ? F("接收模式") : F("发送模式"));

    uint8_t status = commRadio.get_status();
    Serial.print(F("STATUS (0x07): 0x"));
    Serial.println(status, HEX);
    Serial.print(F("  TX_DS: "));
    Serial.println((status & 0x20) ? F("发送完成") : F("未完成"));
    Serial.print(F("  MAX_RT: "));
    Serial.println((status & 0x10) ? F("达到最大重传") : F("正常"));
    Serial.print(F("  RX_DR: "));
    Serial.println((status & 0x40) ? F("接收到数据") : F("无数据"));

    uint8_t fifo_status = commRadio.read_register(0x17);
    Serial.print(F("FIFO_STATUS (0x17): 0x"));
    Serial.println(fifo_status, HEX);
    Serial.print(F("  TX_FULL: "));
    Serial.println((fifo_status & 0x20) ? F("发送FIFO满") : F("发送FIFO未满"));
    Serial.print(F("  TX_EMPTY: "));
    Serial.println((fifo_status & 0x10) ? F("发送FIFO空") : F("发送FIFO非空"));

    // 配置参数
    Serial.println(F("\n--- 配置参数 ---"));
    Serial.print(F("通信频道: "));
    Serial.print(COMM_RF_CHANNEL);
    Serial.print(F(" ("));
    Serial.print(2400 + COMM_RF_CHANNEL);
    Serial.println(F("MHz)"));
    Serial.print(F("功率级别: "));
    Serial.println(F("RF24_PA_HIGH"));
    Serial.print(F("数据速率: "));
    Serial.println(F("RF24_250KBPS"));
    Serial.print(F("通信地址: "));
    Serial.println(COMM_RF_ADDRESS);
    Serial.print(F("自动应答: "));
    Serial.println(F("开启"));
    Serial.print(F("重传设置: "));
    Serial.println(F("3次延迟, 5次重试"));

    // 发送地址检查
    Serial.println(F("\n--- 地址配置 ---"));
    uint8_t tx_addr[5];
    commRadio.read_register_buf(0x10, tx_addr, 5);
    Serial.print(F("TX地址: "));
    for(int i = 4; i >= 0; i--) {
        Serial.print(tx_addr[i], HEX);
        Serial.print(F(" "));
    }
    Serial.println();

    Serial.println(F("=================="));
}

void printTestStats() {
    Serial.println(F("=== RF24测试统计 ==="));
    Serial.print(F("总发送包数: "));
    Serial.println(testStats.totalPackets);
    Serial.print(F("成功发送: "));
    Serial.println(testStats.successPackets);
    Serial.print(F("发送失败: "));
    Serial.println(testStats.failedPackets);
    Serial.print(F("发送成功率: "));
    Serial.print(testStats.successRate, 1);
    Serial.println(F("%"));
    Serial.print(F("连续模式: "));
    Serial.println(testStats.continuousMode ? F("开启") : F("关闭"));
    Serial.print(F("发包间隔: "));
    Serial.print(testStats.packetInterval);
    Serial.println(F("ms"));
    Serial.print(F("运行时间: "));
    Serial.print((millis() - testStats.startTime) / 1000);
    Serial.println(F("秒"));
    if (testStats.lastPacketTime > 0) {
        Serial.print(F("最后发包: "));
        Serial.print((millis() - testStats.lastPacketTime) / 1000);
        Serial.println(F("秒前"));
    }
    Serial.println(F("=================="));
}

// ==================== 诊断函数 ====================
void performDetailedDiagnostics() {
    Serial.println(F("=== NRF24L01详细诊断 ==="));

    // 1. SPI通信测试
    Serial.println(F("1. SPI通信测试"));
    testSPICommunication();

    // 2. 电源检查
    Serial.println(F("2. 电源检查"));
    checkPowerSupply();

    // 3. 寄存器读写测试
    Serial.println(F("3. 寄存器读写测试"));
    testRegisterAccess();

    // 4. 发送管道测试
    Serial.println(F("4. 发送管道测试"));
    testTransmitPipe();

    Serial.println(F("=== 诊断完成 ==="));
}

void checkPowerSupply() {
    Serial.println(F("--- 电源供电检查 ---"));
    Serial.println(F("请检查以下项目:"));
    Serial.println(F("1. NRF24L01 VCC连接到Arduino 3.3V (不是5V!)"));
    Serial.println(F("2. 3.3V电压稳定 (用万用表测量应为3.0-3.6V)"));
    Serial.println(F("3. 添加去耦电容 (VCC和GND之间加100uF+10uF)"));
    Serial.println(F("4. 检查Arduino 3.3V输出能力 (最大150mA)"));
    Serial.println(F("5. 如果电流不足，使用外部3.3V稳压器"));

    // 简单的负载测试
    Serial.println(F("执行简单负载测试..."));
    for(int i = 0; i < 5; i++) {
        bool connected = commRadio.isChipConnected();
        Serial.print(F("连接测试 "));
        Serial.print(i+1);
        Serial.print(F(": "));
        Serial.println(connected ? F("✅") : F("❌"));
        delay(100);
    }
}

void testSPICommunication() {
    Serial.println(F("--- SPI通信测试 ---"));

    // 测试基本寄存器读取
    uint8_t config = commRadio.read_register(0x00);
    Serial.print(F("CONFIG寄存器 (0x00): 0x"));
    Serial.println(config, HEX);

    if (config == 0xFF || config == 0x00) {
        Serial.println(F("❌ SPI通信异常！"));
        Serial.println(F("请检查SPI连接:"));
        Serial.println(F("  CE  -> Pin 9"));
        Serial.println(F("  CSN -> Pin 10"));
        Serial.println(F("  MOSI -> Pin 11"));
        Serial.println(F("  MISO -> Pin 12"));
        Serial.println(F("  SCK -> Pin 13"));
        Serial.println(F("  连线要短且牢固"));
    } else {
        Serial.println(F("✅ SPI通信正常"));
    }
}

void testRegisterAccess() {
    Serial.println(F("--- 寄存器读写测试 ---"));

    // 测试写入和读取RF_CH寄存器
    uint8_t original_channel = commRadio.read_register(0x05);
    Serial.print(F("原始频道: "));
    Serial.println(original_channel);

    // 写入测试值
    uint8_t test_channel = 50;
    commRadio.write_register(0x05, test_channel);
    delay(10);

    // 读取验证
    uint8_t read_channel = commRadio.read_register(0x05);
    Serial.print(F("写入频道: "));
    Serial.print(test_channel);
    Serial.print(F(", 读取频道: "));
    Serial.println(read_channel);

    if (read_channel == test_channel) {
        Serial.println(F("✅ 寄存器读写正常"));
    } else {
        Serial.println(F("❌ 寄存器读写异常"));
    }

    // 恢复原始值
    commRadio.write_register(0x05, original_channel);
}

void testTransmitPipe() {
    Serial.println(F("--- 发送管道测试 ---"));

    // 检查发送地址设置
    uint8_t tx_addr[5];
    commRadio.read_register_buf(0x10, tx_addr, 5);
    Serial.print(F("发送地址: "));
    for(int i = 4; i >= 0; i--) {
        if (tx_addr[i] < 0x10) Serial.print(F("0"));
        Serial.print(tx_addr[i], HEX);
        Serial.print(F(" "));
    }
    Serial.println();

    // 检查FIFO状态
    uint8_t fifo_status = commRadio.read_register(0x17);
    Serial.print(F("FIFO状态: 0x"));
    Serial.println(fifo_status, HEX);

    if (fifo_status & 0x20) {
        Serial.println(F("❌ 发送FIFO已满"));
        Serial.println(F("执行FIFO清空..."));
        commRadio.flush_tx();
    } else {
        Serial.println(F("✅ 发送FIFO正常"));
    }
}
