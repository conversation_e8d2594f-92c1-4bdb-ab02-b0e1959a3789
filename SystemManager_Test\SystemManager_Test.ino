/*
 * 便携式电子对抗模拟训练系统 - SystemManager测试程序
 *
 * 已验证功能:
 * ✅ DHT11温湿度传感器 (28.80°C, 32.00%湿度)
 * ✅ 电池电压监测 (A0悬空时使用7400mV模拟电压)
 * ✅ 温度报警逻辑 (>60°C触发0x0004错误代码)
 * ✅ 系统状态管理 (初始化→就绪→错误状态切换)
 * ✅ LED状态指示 (电源/通信/错误LED)
 * ✅ 错误处理机制 (低电压/高温报警)
 * ✅ 性能监控 (循环时间统计)
 *
 * 硬件连接:
 * - D5: 电源LED (绿色) + 220Ω电阻
 * - D6: 通信LED (蓝色) + 220Ω电阻
 * - D7: 错误LED (红色) + 220Ω电阻
 * - D8: DHT11温湿度传感器 + 4.7kΩ上拉电阻
 * - A0: 电池电压监测 (悬空时自动使用模拟电压)
 *
 * 测试验证结果:
 * - DHT11传感器: 正常工作，读取真实环境数据
 * - 电压检测: 修复悬空时的低电压误报问题
 * - 温度报警: 逻辑正确，需物理加热>60°C测试
 * - 系统健康: 综合监控电压、温度、错误状态
 */

#include <DHT.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

// 模拟Config.h中的定义 (测试用)
#define DEBUG_PRINT(x) Serial.print(x)
#define DEBUG_PRINTLN(x) Serial.println(x)

// LED引脚定义
#define STATUS_LED_POWER 5
#define STATUS_LED_COMM 6
#define STATUS_LED_ERROR 7

// 传感器引脚定义
#define BATTERY_MONITOR_PIN A0
#define DHT_PIN 8
#define DHT_TYPE DHT11  // 修改为DHT11传感器

// OLED显示屏配置
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1
#define OLED_I2C_ADDRESS 0x3C

// 系统安全配置
#define MAX_TEMPERATURE 60  // 恢复正常温度报警阈值
#define MIN_BATTERY_VOLTAGE 6500
#define CRITICAL_BATTERY_VOLTAGE 6000

// 错误代码定义
#define ERROR_EMERGENCY_STOP 0x0042

// 系统状态枚举
enum SystemState {
    SYS_INIT = 0,
    SYS_READY = 1,
    SYS_TRAINING = 2,
    SYS_ERROR = 3,
    SYS_SHUTDOWN = 4
};

// 系统状态结构
struct SystemStatus {
    SystemState state;
    unsigned long uptime;
    uint16_t batteryVoltage;
    int8_t temperature;
    uint16_t errorCode;
    bool slaveConnected;
    unsigned long timestamp;
};

// 包含SystemManager类定义 (简化版)
class SystemManager {
private:
    SystemState currentState;
    SystemState previousState;
    unsigned long stateChangeTime;
    unsigned long systemStartTime;
    uint16_t batteryVoltage;
    int8_t systemTemperature;
    uint16_t currentErrorCode;
    bool powerLEDState;
    bool commLEDState;
    bool errorLEDState;
    unsigned long lastLEDUpdate;
    unsigned long loopStartTime;
    unsigned long maxLoopTime;
    unsigned long totalLoops;

    // DHT传感器相关
    float humidity;
    bool dhtInitialized;
    unsigned long lastDHTRead;

    // OLED显示相关
    bool oledInitialized;
    unsigned long lastOLEDUpdate;

    void updateBatteryVoltage();
    void updateTemperature();
    void updateLEDs();
    void checkSystemHealth();
    int freeMemory();
    bool initDHTSensor();
    bool readDHTSensor();

    // OLED显示方法
    bool initOLED();
    void updateOLEDDisplay();
    void drawSystemStatus();
    void drawSensorData();
    void drawErrorMessage(uint16_t errorCode);

public:
    SystemManager();
    bool init();
    void update();
    
    // 状态管理
    void setState(SystemState newState);
    SystemState getState() const { return currentState; }
    String getStateString() const;
    unsigned long getStateTime() const;
    
    // 系统信息
    SystemStatus getSystemStatus() const;
    unsigned long getUptime() const;
    uint16_t getBatteryVoltage() const { return batteryVoltage; }
    int8_t getTemperature() const { return systemTemperature; }
    float getHumidity() const { return humidity; }
    uint8_t getBatteryPercent() const;
    String getUptimeString() const;
    
    // 错误管理
    void setErrorCode(uint16_t errorCode);
    uint16_t getErrorCode() const { return currentErrorCode; }
    void clearError();
    String getErrorString(uint16_t errorCode) const;
    
    // LED控制
    void setPowerLED(bool state);
    void setCommLED(bool state);
    void setErrorLED(bool state);
    void toggleLED(uint8_t ledNumber);
    bool testLEDs();
    
    // 系统控制
    void emergencyStop();
    void softReset();
    void shutdown();
    
    // 性能监控
    void startPerformanceMonitor();
    void endPerformanceMonitor();
    void printPerformanceStats() const;
    
    // 系统健康检查
    bool isSystemHealthy() const;
    bool isBatteryLow() const;
    bool isOverTemperature() const;
    
    // 调试功能
    void printSystemInfo() const;
    void runDiagnostics();
};

// 全局实例
SystemManager systemMgr;
DHT dht(DHT_PIN, DHT_TYPE);
Adafruit_SSD1306 oled(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);

void setup() {
    Serial.begin(115200);
    
    Serial.println(F("=== SystemManager功能测试 ==="));
    Serial.println(F("1. 系统状态管理"));
    Serial.println(F("2. 硬件监控功能"));
    Serial.println(F("3. LED控制"));
    Serial.println(F("4. 错误处理"));
    Serial.println(F("5. 性能监控"));
    Serial.println(F("============================"));
    
    // 初始化SystemManager
    if(systemMgr.init()) {
        Serial.println(F("SystemManager初始化成功"));
    } else {
        Serial.println(F("SystemManager初始化失败"));
    }
    
    // 运行初始诊断
    systemMgr.runDiagnostics();
}

void loop() {
    // 更新SystemManager
    systemMgr.update();
    
    // 定期打印系统信息 (每5秒)
    static unsigned long lastInfoPrint = 0;
    if(millis() - lastInfoPrint >= 5000) {
        lastInfoPrint = millis();
        systemMgr.printSystemInfo();
    }
    
    // 处理串口命令
    handleSerialCommands();
    
    // 模拟系统负载
    delay(10);
}

void handleSerialCommands() {
    if(Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if(command == "info") {
            systemMgr.printSystemInfo();
        } else if(command == "diag") {
            systemMgr.runDiagnostics();
        } else if(command == "led") {
            systemMgr.testLEDs();
        } else if(command == "error") {
            systemMgr.setErrorCode(0x0001);
            Serial.println(F("设置测试错误"));
        } else if(command == "clear") {
            systemMgr.clearError();
            Serial.println(F("清除错误"));
        } else if(command == "stop") {
            systemMgr.emergencyStop();
            Serial.println(F("紧急停止"));
        } else if(command == "reset") {
            systemMgr.softReset();
            Serial.println(F("软件重启"));
        } else if(command == "shutdown") {
            systemMgr.shutdown();
            Serial.println(F("系统关机"));
        } else if(command == "training") {
            systemMgr.setState(SYS_TRAINING);
            Serial.println(F("进入训练模式"));
        } else if(command == "ready") {
            systemMgr.setState(SYS_READY);
            Serial.println(F("进入就绪模式"));
        } else if(command == "perf") {
            systemMgr.printPerformanceStats();
        } else if(command == "help") {
            Serial.println(F("可用命令:"));
            Serial.println(F("info - 显示系统信息"));
            Serial.println(F("diag - 运行系统诊断"));
            Serial.println(F("led - LED测试"));
            Serial.println(F("error - 设置测试错误"));
            Serial.println(F("clear - 清除错误"));
            Serial.println(F("stop - 紧急停止"));
            Serial.println(F("reset - 软件重启"));
            Serial.println(F("shutdown - 系统关机"));
            Serial.println(F("training - 进入训练模式"));
            Serial.println(F("ready - 进入就绪模式"));
            Serial.println(F("perf - 显示性能统计"));
            Serial.println(F("help - 显示帮助"));
        } else {
            Serial.println(F("未知命令，输入help查看帮助"));
        }
    }
}

// SystemManager简化实现 (仅用于测试)
SystemManager::SystemManager() :
    currentState(SYS_INIT),
    previousState(SYS_INIT),
    stateChangeTime(0),
    systemStartTime(0),
    batteryVoltage(7400),
    systemTemperature(25),
    currentErrorCode(0),
    powerLEDState(false),
    commLEDState(false),
    errorLEDState(false),
    lastLEDUpdate(0),
    loopStartTime(0),
    maxLoopTime(0),
    totalLoops(0),
    humidity(50.0),
    dhtInitialized(false),
    lastDHTRead(0),
    oledInitialized(false),
    lastOLEDUpdate(0)
{
}

bool SystemManager::init() {
    systemStartTime = millis();
    stateChangeTime = systemStartTime;

    pinMode(STATUS_LED_POWER, OUTPUT);
    pinMode(STATUS_LED_COMM, OUTPUT);
    pinMode(STATUS_LED_ERROR, OUTPUT);

    // 初始化DHT传感器
    dhtInitialized = initDHTSensor();
    if(dhtInitialized) {
        Serial.println(F("DHT传感器初始化成功"));
    } else {
        Serial.println(F("DHT传感器初始化失败，使用模拟数据"));
    }

    // 初始化OLED显示屏
    oledInitialized = initOLED();
    if(oledInitialized) {
        Serial.println(F("OLED显示屏初始化成功"));
    } else {
        Serial.println(F("OLED显示屏初始化失败"));
    }

    setState(SYS_READY);
    setPowerLED(true);

    updateBatteryVoltage();
    updateTemperature();

    return true;
}

void SystemManager::update() {
    startPerformanceMonitor();
    
    static unsigned long lastStatusUpdate = 0;
    if(millis() - lastStatusUpdate >= 1000) {
        lastStatusUpdate = millis();
        updateBatteryVoltage();
        updateTemperature();
        checkSystemHealth();
    }
    
    if(millis() - lastLEDUpdate >= 100) {
        lastLEDUpdate = millis();
        updateLEDs();
    }

    // 更新OLED显示 (每2秒更新一次)
    if(oledInitialized && millis() - lastOLEDUpdate >= 2000) {
        lastOLEDUpdate = millis();
        updateOLEDDisplay();
    }
    
    endPerformanceMonitor();
}

void SystemManager::setState(SystemState newState) {
    if(newState != currentState) {
        previousState = currentState;
        currentState = newState;
        stateChangeTime = millis();
        
        Serial.print(F("状态变更: "));
        Serial.print((int)previousState);
        Serial.print(F(" -> "));
        Serial.println((int)currentState);
    }
}

String SystemManager::getStateString() const {
    switch(currentState) {
        case SYS_INIT: return "INIT";
        case SYS_READY: return "READY";
        case SYS_TRAINING: return "TRAINING";
        case SYS_ERROR: return "ERROR";
        case SYS_SHUTDOWN: return "SHUTDOWN";
        default: return "UNKNOWN";
    }
}

unsigned long SystemManager::getUptime() const {
    return millis() - systemStartTime;
}

uint8_t SystemManager::getBatteryPercent() const {
    uint8_t percent = map(batteryVoltage, 6000, 8400, 0, 100);
    return constrain(percent, 0, 100);
}

String SystemManager::getUptimeString() const {
    unsigned long uptime = getUptime();
    unsigned long hours = uptime / 3600000;
    unsigned long minutes = (uptime % 3600000) / 60000;
    unsigned long seconds = (uptime % 60000) / 1000;
    
    String result = "";
    if(hours < 10) result += "0";
    result += String(hours) + ":";
    if(minutes < 10) result += "0";
    result += String(minutes) + ":";
    if(seconds < 10) result += "0";
    result += String(seconds);
    
    return result;
}

void SystemManager::setErrorCode(uint16_t errorCode) {
    currentErrorCode = errorCode;
    if(errorCode != 0) {
        setState(SYS_ERROR);
        setErrorLED(true);
    }
}

void SystemManager::clearError() {
    currentErrorCode = 0;
    setErrorLED(false);
    if(currentState == SYS_ERROR) {
        setState(SYS_READY);
    }
}

String SystemManager::getErrorString(uint16_t errorCode) const {
    switch(errorCode) {
        case 0x0001: return "Config Init Failed";
        case 0x0002: return "Comm Timeout";
        case 0x0003: return "Battery Low";
        case 0x0004: return "Temperature High";
        case 0x0042: return "Emergency Stop";
        default: return "Unknown Error";
    }
}

void SystemManager::setPowerLED(bool state) {
    powerLEDState = state;
    digitalWrite(STATUS_LED_POWER, state ? HIGH : LOW);
}

void SystemManager::setCommLED(bool state) {
    commLEDState = state;
    digitalWrite(STATUS_LED_COMM, state ? HIGH : LOW);
}

void SystemManager::setErrorLED(bool state) {
    errorLEDState = state;
    digitalWrite(STATUS_LED_ERROR, state ? HIGH : LOW);
}

bool SystemManager::testLEDs() {
    Serial.println(F("LED测试开始"));

    setPowerLED(true);
    delay(200);
    setCommLED(true);
    delay(200);
    setErrorLED(true);
    delay(200);

    setPowerLED(false);
    setCommLED(false);
    setErrorLED(false);
    delay(200);

    setPowerLED(true);

    Serial.println(F("LED测试完成"));
    return true;
}

void SystemManager::emergencyStop() {
    setState(SYS_ERROR);
    setErrorCode(ERROR_EMERGENCY_STOP);
    setPowerLED(false);
    setCommLED(false);
    setErrorLED(true);
}

void SystemManager::softReset() {
    clearError();
    setState(SYS_INIT);
    delay(100);
    setState(SYS_READY);
    setPowerLED(true);
    setCommLED(false);
    setErrorLED(false);
}

void SystemManager::shutdown() {
    setState(SYS_SHUTDOWN);
    setPowerLED(false);
    setCommLED(false);
    setErrorLED(false);
}

void SystemManager::startPerformanceMonitor() {
    loopStartTime = micros();
}

void SystemManager::endPerformanceMonitor() {
    unsigned long loopTime = micros() - loopStartTime;
    if(loopTime > maxLoopTime) {
        maxLoopTime = loopTime;
    }
    totalLoops++;
}

void SystemManager::printPerformanceStats() const {
    Serial.print(F("性能统计 - 最大循环时间: "));
    Serial.print(maxLoopTime);
    Serial.print(F("μs, 总循环次数: "));
    Serial.println(totalLoops);
}

bool SystemManager::isSystemHealthy() const {
    return (currentErrorCode == 0 && !isBatteryLow() && !isOverTemperature());
}

bool SystemManager::isBatteryLow() const {
    return batteryVoltage < MIN_BATTERY_VOLTAGE;
}

bool SystemManager::isOverTemperature() const {
    return systemTemperature > MAX_TEMPERATURE;
}

void SystemManager::printSystemInfo() const {
    Serial.println(F("=== 系统信息 ==="));
    Serial.print(F("状态: "));
    Serial.println(getStateString());
    Serial.print(F("运行时间: "));
    Serial.println(getUptimeString());
    Serial.print(F("温度: "));
    Serial.print(systemTemperature);
    Serial.println(F("°C"));
    Serial.print(F("湿度: "));
    Serial.print(humidity);
    Serial.println(F("%"));
    Serial.print(F("DHT传感器: "));
    Serial.println(dhtInitialized ? "正常" : "离线");
    Serial.print(F("电池电压: "));
    Serial.print(batteryVoltage);
    Serial.println(F("mV"));
    Serial.print(F("电池百分比: "));
    Serial.print(getBatteryPercent());
    Serial.println(F("%"));
    Serial.print(F("错误代码: 0x"));
    Serial.println(currentErrorCode, HEX);
    Serial.print(F("系统健康: "));
    Serial.println(isSystemHealthy() ? "正常" : "异常");
    Serial.println(F("==============="));
}

void SystemManager::runDiagnostics() {
    Serial.println(F("开始系统诊断..."));

    testLEDs();

    updateBatteryVoltage();
    if(isBatteryLow()) {
        Serial.println(F("警告: 电池电压过低"));
    }

    updateTemperature();
    if(isOverTemperature()) {
        Serial.println(F("警告: 系统温度过高"));
    }

    printPerformanceStats();

    Serial.println(F("系统诊断完成"));
}

void SystemManager::updateBatteryVoltage() {
    int adcValue = analogRead(BATTERY_MONITOR_PIN);

    // 调试输出ADC原始值
    static unsigned long lastDebugPrint = 0;
    if(millis() - lastDebugPrint >= 5000) { // 每5秒打印一次
        lastDebugPrint = millis();
        Serial.print(F("ADC原始值: "));
        Serial.print(adcValue);
        Serial.print(F(" ("));
        Serial.print((adcValue * 5000.0) / 1023.0);
        Serial.println(F("mV)"));
    }

    // 修复：更稳定的电压检测逻辑
    static bool isTestMode = false;
    static unsigned long testModeStartTime = 0;

    if(adcValue < 100) { // 更严格的阈值，确实是接地或严重悬空
        if(!isTestMode) {
            isTestMode = true;
            testModeStartTime = millis();
            Serial.println(F("检测到A0接地，进入测试模式"));
        }

        // 接地状态：模拟低电压触发报警测试
        batteryVoltage = 5500; // 低于6500mV阈值，触发报警

        static unsigned long lastLowVoltageMsg = 0;
        if(millis() - lastLowVoltageMsg >= 10000) {
            lastLowVoltageMsg = millis();
            Serial.println(F("测试模式: 模拟低电压5500mV (触发报警)"));
        }
    } else if(adcValue < 500) { // 悬空状态
        if(isTestMode && millis() - testModeStartTime > 5000) {
            // 从接地状态恢复到悬空状态，延迟5秒后切换到正常模拟电压
            isTestMode = false;
            Serial.println(F("退出测试模式，使用正常模拟电压"));
        }

        if(!isTestMode) {
            batteryVoltage = 7400; // 正常模拟电压
            static unsigned long lastSimMsg = 0;
            if(millis() - lastSimMsg >= 10000) {
                lastSimMsg = millis();
                Serial.println(F("正常模式: 使用模拟电压7400mV"));
            }
        }
    } else {
        // 正常分压电路检测
        isTestMode = false;
        batteryVoltage = map(adcValue, 614, 860, 6000, 8400);
        batteryVoltage = constrain(batteryVoltage, 5000, 9000);

        static unsigned long lastRealMsg = 0;
        if(millis() - lastRealMsg >= 10000) {
            lastRealMsg = millis();
            Serial.println(F("实际模式: 使用分压电路检测"));
        }
    }

    // 电压报警检查
    if(isBatteryLow() && currentErrorCode == 0) {
        Serial.print(F("⚠️ 电池电压过低: "));
        Serial.print(batteryVoltage);
        Serial.print(F("mV < "));
        Serial.print(MIN_BATTERY_VOLTAGE);
        Serial.println(F("mV"));
        setErrorCode(0x0003);
    }
}

void SystemManager::updateTemperature() {
    if(dhtInitialized && readDHTSensor()) {
        // 使用真实的DHT传感器数据
        // readDHTSensor()已经更新了systemTemperature和humidity
    } else {
        // 使用模拟数据作为备用
        unsigned long uptime = getUptime();
        int baseTemp = 25;

        // 模拟温度变化：每分钟变化，范围25-70°C (包含超过60°C的情况)
        int tempVariation = (uptime / 60000) % 46; // 0-45的变化范围
        systemTemperature = baseTemp + tempVariation; // 25-70°C

        humidity = 50.0 + (uptime / 30000) % 30; // 模拟湿度变化

        // 调试输出温度变化
        static int8_t lastReportedTemp = -1;
        if(systemTemperature != lastReportedTemp) {
            lastReportedTemp = systemTemperature;
            Serial.print(F("模拟温度更新: "));
            Serial.print(systemTemperature);
            Serial.print(F("°C (运行时间: "));
            Serial.print(uptime / 1000);
            Serial.println(F("秒)"));
        }
    }

    // 温度报警检查和调试
    if(isOverTemperature()) {
        Serial.print(F("⚠️ 温度报警: "));
        Serial.print(systemTemperature);
        Serial.print(F("°C > "));
        Serial.print(MAX_TEMPERATURE);
        Serial.println(F("°C"));

        if(currentErrorCode == 0) {
            Serial.println(F("设置温度报警错误代码 0x0004"));
            setErrorCode(0x0004);
        }
    }
}

void SystemManager::updateLEDs() {
    switch(currentState) {
        case SYS_INIT:
            setPowerLED((millis() / 500) % 2);
            break;
        case SYS_READY:
            setPowerLED(true);
            break;
        case SYS_TRAINING:
            setCommLED((millis() / 200) % 2);
            break;
        case SYS_ERROR:
            setErrorLED((millis() / 300) % 2);
            break;
        case SYS_SHUTDOWN:
            setPowerLED(false);
            setCommLED(false);
            setErrorLED(false);
            break;
    }
}

void SystemManager::checkSystemHealth() {
    bool batteryOK = !isBatteryLow();
    bool tempOK = !isOverTemperature();

    // 修复：添加状态稳定性检查，避免快速切换
    static unsigned long lastErrorTime = 0;
    static unsigned long lastClearTime = 0;

    // 如果系统恢复正常，但需要等待一定时间才清除错误（避免抖动）
    if(batteryOK && tempOK && currentErrorCode != 0) {
        if(currentErrorCode == 0x0003 || currentErrorCode == 0x0004) {
            // 确保错误状态至少持续3秒，避免快速切换
            if(millis() - lastErrorTime > 3000) {
                Serial.print(F("系统恢复正常，清除错误代码: 0x"));
                Serial.println(currentErrorCode, HEX);
                clearError();
                lastClearTime = millis();
            }
        }
    }

    // 记录错误设置时间
    if(currentErrorCode != 0 && lastErrorTime == 0) {
        lastErrorTime = millis();
    } else if(currentErrorCode == 0) {
        lastErrorTime = 0;
    }
}

// DHT传感器相关函数实现
bool SystemManager::initDHTSensor() {
    Serial.println(F("初始化DHT传感器..."));
    dht.begin();
    delay(1000); // DHT11传感器启动时间

    // 尝试读取一次数据验证传感器是否正常
    float testTemp = dht.readTemperature();
    float testHum = dht.readHumidity();

    if(isnan(testTemp) || isnan(testHum)) {
        Serial.println(F("DHT传感器读取失败"));
        return false;
    }

    Serial.print(F("DHT传感器正常，初始读数 - 温度: "));
    Serial.print(testTemp);
    Serial.print(F("°C, 湿度: "));
    Serial.print(testHum);
    Serial.println(F("%"));

    return true;
}

bool SystemManager::readDHTSensor() {
    // DHT传感器读取间隔限制 (DHT11最快1秒一次)
    if(millis() - lastDHTRead < 1000) {
        return true; // 使用上次的数据
    }

    float temp = dht.readTemperature();
    float hum = dht.readHumidity();

    if(isnan(temp) || isnan(hum)) {
        Serial.println(F("DHT传感器读取失败"));
        return false;
    }

    // 更新数据
    systemTemperature = (int8_t)temp;
    humidity = hum;
    lastDHTRead = millis();

    // 调试输出 (可选)
    static unsigned long lastDebugPrint = 0;
    if(millis() - lastDebugPrint >= 10000) { // 每10秒打印一次
        lastDebugPrint = millis();
        Serial.print(F("DHT读取 - 温度: "));
        Serial.print(temp);
        Serial.print(F("°C, 湿度: "));
        Serial.print(hum);
        Serial.println(F("%"));
    }

    return true;
}

int SystemManager::freeMemory() {
    // 简化的内存检测实现
    extern int __heap_start, *__brkval;
    int v;
    return (int) &v - (__brkval == 0 ? (int) &__heap_start : (int) __brkval);
}

// OLED显示相关方法实现
bool SystemManager::initOLED() {
    Serial.println(F("初始化OLED显示屏..."));

    // 初始化I2C
    Wire.begin();

    // 初始化OLED显示屏
    if(!oled.begin(SSD1306_SWITCHCAPVCC, OLED_I2C_ADDRESS)) {
        Serial.println(F("OLED初始化失败"));
        return false;
    }

    // 清屏并显示启动信息
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);
    oled.println(F("ECM Training System"));
    oled.println(F("SystemManager Test"));
    oled.println();
    oled.println(F("OLED Ready!"));
    oled.display();

    delay(2000); // 显示启动信息2秒

    Serial.println(F("OLED初始化成功"));
    return true;
}

void SystemManager::updateOLEDDisplay() {
    if(!oledInitialized) return;

    // 根据系统状态选择显示内容
    if(currentErrorCode != 0) {
        drawErrorMessage(currentErrorCode);
    } else {
        drawSystemStatus();
    }
}

void SystemManager::drawSystemStatus() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);

    // 标题
    oled.setCursor(0, 0);
    oled.println(F("System Status"));
    oled.drawLine(0, 10, 127, 10, SSD1306_WHITE);

    // 第一行：温度和湿度
    oled.setCursor(0, 16);
    oled.print(F("Temp: "));
    oled.print(systemTemperature);
    oled.print(F("C"));

    oled.setCursor(70, 16);
    oled.print(F("Hum: "));
    oled.print((int)humidity);
    oled.print(F("%"));

    // 第二行：电池电压和百分比
    oled.setCursor(0, 26);
    oled.print(F("Batt: "));
    oled.print(batteryVoltage);
    oled.print(F("mV"));

    oled.setCursor(70, 26);
    oled.print(F("("));
    oled.print(getBatteryPercent());
    oled.print(F("%)"));

    // 第三行：系统状态
    oled.setCursor(0, 36);
    oled.print(F("State: "));
    oled.print(getStateString());

    // 第四行：运行时间
    oled.setCursor(0, 46);
    oled.print(F("Uptime: "));
    oled.print(getUptimeString());

    // 第五行：传感器状态
    oled.setCursor(0, 56);
    oled.print(F("DHT: "));
    oled.print(dhtInitialized ? "OK" : "ERR");

    oled.setCursor(50, 56);
    oled.print(F("Health: "));
    oled.print(isSystemHealthy() ? "OK" : "ERR");

    oled.display();
}

void SystemManager::drawSensorData() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);

    // 标题
    oled.setCursor(0, 0);
    oled.println(F("Sensor Data"));
    oled.drawLine(0, 10, 127, 10, SSD1306_WHITE);

    // 温度 (大字体显示)
    oled.setTextSize(2);
    oled.setCursor(0, 16);
    oled.print(systemTemperature);
    oled.print(F("C"));

    // 湿度 (大字体显示)
    oled.setCursor(0, 36);
    oled.print((int)humidity);
    oled.print(F("%"));

    // 电压状态
    oled.setTextSize(1);
    oled.setCursor(0, 56);
    oled.print(F("Battery: "));
    oled.print(batteryVoltage);
    oled.print(F("mV"));

    oled.display();
}

void SystemManager::drawErrorMessage(uint16_t errorCode) {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);

    // 错误标题 (闪烁效果)
    if((millis() / 500) % 2) {
        oled.setCursor(0, 0);
        oled.println(F("*** SYSTEM ERROR ***"));
    }
    oled.drawLine(0, 10, 127, 10, SSD1306_WHITE);

    // 错误代码
    oled.setCursor(0, 16);
    oled.print(F("Error Code: 0x"));
    oled.println(errorCode, HEX);

    // 错误描述
    oled.setCursor(0, 26);
    String errorMsg = getErrorString(errorCode);
    oled.println(errorMsg);

    // 当前状态
    oled.setCursor(0, 40);
    oled.print(F("Temp: "));
    oled.print(systemTemperature);
    oled.print(F("C  Batt: "));
    oled.print(getBatteryPercent());
    oled.print(F("%"));

    // 提示信息
    oled.setCursor(0, 56);
    oled.println(F("Check system status"));

    oled.display();
}
