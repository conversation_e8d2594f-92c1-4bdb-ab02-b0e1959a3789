/*
 * SystemManager + OLED集成测试程序
 * 验证OLED显示与传感器数据的集成功能
 */

#include <DHT.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1315.h>

// 引入统一配置文件
#include "../Master_Controller/Config.h"

// OLED显示屏配置
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1

// 系统配置
#define MAX_TEMPERATURE 60
#define MIN_BATTERY_VOLTAGE 6500

// 全局实例
DHT dht(DHT_PIN, DHT_TYPE);
Adafruit_SSD1315 oled(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);

// 系统变量
bool dhtInitialized = false;
bool oledInitialized = false;
unsigned long systemStartTime = 0;
unsigned long lastOLEDUpdate = 0;
unsigned long lastSensorRead = 0;

// 传感器数据
float temperature = 25.0;
float humidity = 50.0;
uint16_t batteryVoltage = 7400;
uint8_t batteryPercent = 75;
bool systemHealthy = true;
uint16_t errorCode = 0;

void setup() {
    Serial.begin(115200);
    Serial.println("=== SystemManager + OLED集成测试 ===");
    Serial.println();
    
    systemStartTime = millis();
    
    // 初始化LED引脚
    pinMode(STATUS_LED_POWER, OUTPUT);
    pinMode(STATUS_LED_COMM, OUTPUT);
    pinMode(STATUS_LED_ERROR, OUTPUT);
    
    // 初始化DHT传感器
    Serial.println("初始化DHT11传感器...");
    dht.begin();
    delay(1000);
    
    float testTemp = dht.readTemperature();
    float testHum = dht.readHumidity();
    
    if(!isnan(testTemp) && !isnan(testHum)) {
        dhtInitialized = true;
        temperature = testTemp;
        humidity = testHum;
        Serial.println("DHT11传感器初始化成功");
        digitalWrite(STATUS_LED_POWER, HIGH);
    } else {
        Serial.println("DHT11传感器初始化失败，使用模拟数据");
        digitalWrite(STATUS_LED_ERROR, HIGH);
    }
    
    // 初始化OLED显示屏
    Serial.println("初始化OLED显示屏...");
    Wire.begin();
    
    if(oled.begin(SSD1315_SWITCHCAPVCC, OLED_I2C_ADDRESS)) {
        oledInitialized = true;
        Serial.println("OLED显示屏初始化成功");
        
        // 显示启动信息
        showStartupScreen();
        delay(3000);
        
        digitalWrite(STATUS_LED_COMM, HIGH);
    } else {
        Serial.println("OLED显示屏初始化失败");
        Serial.println("请检查I2C连接 (SDA→A4, SCL→A5)");
    }
    
    Serial.println("系统初始化完成");
    Serial.println();
}

void loop() {
    // 每秒更新传感器数据
    if(millis() - lastSensorRead >= 1000) {
        lastSensorRead = millis();
        updateSensorData();
        updateSystemHealth();
    }
    
    // 每2秒更新OLED显示
    if(oledInitialized && millis() - lastOLEDUpdate >= 2000) {
        lastOLEDUpdate = millis();
        updateOLEDDisplay();
    }
    
    // 更新LED状态
    updateLEDs();
    
    // 每5秒打印系统信息到串口
    static unsigned long lastSerialPrint = 0;
    if(millis() - lastSerialPrint >= 5000) {
        lastSerialPrint = millis();
        printSystemInfo();
    }
    
    delay(100);
}

void updateSensorData() {
    // 读取DHT传感器
    if(dhtInitialized) {
        float temp = dht.readTemperature();
        float hum = dht.readHumidity();
        
        if(!isnan(temp) && !isnan(hum)) {
            temperature = temp;
            humidity = hum;
        }
    } else {
        // 使用模拟数据
        unsigned long uptime = millis() - systemStartTime;
        temperature = 25.0 + (uptime / 60000) % 15; // 25-40°C变化
        humidity = 50.0 + (uptime / 30000) % 30;    // 50-80%变化
    }
    
    // 读取电池电压
    int adcValue = analogRead(BATTERY_MONITOR_PIN);
    if(adcValue < 500) {
        batteryVoltage = 7400; // 模拟电压
    } else {
        batteryVoltage = map(adcValue, 614, 860, 6000, 8400);
        batteryVoltage = constrain(batteryVoltage, 5000, 9000);
    }
    
    batteryPercent = map(batteryVoltage, 6000, 8400, 0, 100);
    batteryPercent = constrain(batteryPercent, 0, 100);
}

void updateSystemHealth() {
    errorCode = 0;
    
    // 检查温度
    if(temperature > MAX_TEMPERATURE) {
        errorCode = 0x0004; // 高温报警
    }
    
    // 检查电池电压
    if(batteryVoltage < MIN_BATTERY_VOLTAGE) {
        errorCode = 0x0003; // 低电压报警
    }
    
    systemHealthy = (errorCode == 0);
}

void updateOLEDDisplay() {
    if(!oledInitialized) return;
    
    if(errorCode != 0) {
        showErrorScreen();
    } else {
        showSystemStatus();
    }
}

void showStartupScreen() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1315_WHITE);
    oled.setCursor(0, 0);
    oled.println("ECM Training System");
    oled.println("SystemManager Test");
    oled.println();
    oled.println("Initializing...");
    oled.println();
    oled.print("DHT11: ");
    oled.println(dhtInitialized ? "OK" : "FAIL");
    oled.print("OLED:  OK");
    oled.display();
}

void showSystemStatus() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    
    // 标题
    oled.setCursor(0, 0);
    oled.println("System Status");
    oled.drawLine(0, 10, 127, 10, SSD1315_WHITE);
    
    // 第一行：温度和湿度
    oled.setCursor(0, 16);
    oled.print("Temp: ");
    oled.print(temperature, 1);
    oled.print("C");
    
    oled.setCursor(70, 16);
    oled.print("Hum: ");
    oled.print(humidity, 0);
    oled.print("%");
    
    // 第二行：电池状态
    oled.setCursor(0, 26);
    oled.print("Batt: ");
    oled.print(batteryVoltage);
    oled.print("mV (");
    oled.print(batteryPercent);
    oled.print("%)");
    
    // 第三行：传感器状态
    oled.setCursor(0, 36);
    oled.print("DHT: ");
    oled.print(dhtInitialized ? "OK" : "ERR");
    
    oled.setCursor(50, 36);
    oled.print("Health: ");
    oled.print(systemHealthy ? "OK" : "ERR");
    
    // 第四行：运行时间
    oled.setCursor(0, 46);
    oled.print("Uptime: ");
    unsigned long uptime = (millis() - systemStartTime) / 1000;
    oled.print(uptime / 60);
    oled.print(":");
    if(uptime % 60 < 10) oled.print("0");
    oled.print(uptime % 60);
    
    // 第五行：状态指示
    oled.setCursor(0, 56);
    oled.print("Status: NORMAL");
    
    oled.display();
}

void showErrorScreen() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    
    // 错误标题 (闪烁效果)
    if((millis() / 500) % 2) {
        oled.setCursor(0, 0);
        oled.println("*** SYSTEM ERROR ***");
    }
    oled.drawLine(0, 10, 127, 10, SSD1306_WHITE);
    
    // 错误代码
    oled.setCursor(0, 16);
    oled.print("Error Code: 0x");
    oled.println(errorCode, HEX);
    
    // 错误描述
    oled.setCursor(0, 26);
    if(errorCode == 0x0003) {
        oled.println("Battery Low");
    } else if(errorCode == 0x0004) {
        oled.println("Temperature High");
    }
    
    // 当前状态
    oled.setCursor(0, 40);
    oled.print("Temp: ");
    oled.print(temperature, 1);
    oled.print("C  Batt: ");
    oled.print(batteryPercent);
    oled.print("%");
    
    // 提示信息
    oled.setCursor(0, 56);
    oled.println("Check system!");
    
    oled.display();
}

void updateLEDs() {
    // 电源LED：系统正常时常亮
    digitalWrite(STATUS_LED_POWER, systemHealthy ? HIGH : LOW);
    
    // 通信LED：模拟通信状态
    static unsigned long lastCommToggle = 0;
    if(millis() - lastCommToggle >= 1000) {
        lastCommToggle = millis();
        static bool commState = false;
        commState = !commState;
        digitalWrite(STATUS_LED_COMM, commState ? HIGH : LOW);
    }
    
    // 错误LED：有错误时闪烁
    if(errorCode != 0) {
        static unsigned long lastErrorToggle = 0;
        if(millis() - lastErrorToggle >= 300) {
            lastErrorToggle = millis();
            static bool errorState = false;
            errorState = !errorState;
            digitalWrite(STATUS_LED_ERROR, errorState ? HIGH : LOW);
        }
    } else {
        digitalWrite(STATUS_LED_ERROR, LOW);
    }
}

void printSystemInfo() {
    Serial.println("=== 系统状态信息 ===");
    Serial.print("温度: ");
    Serial.print(temperature);
    Serial.println("°C");
    Serial.print("湿度: ");
    Serial.print(humidity);
    Serial.println("%");
    Serial.print("电池电压: ");
    Serial.print(batteryVoltage);
    Serial.print("mV (");
    Serial.print(batteryPercent);
    Serial.println("%)");
    Serial.print("DHT传感器: ");
    Serial.println(dhtInitialized ? "正常" : "离线");
    Serial.print("OLED显示: ");
    Serial.println(oledInitialized ? "正常" : "离线");
    Serial.print("系统健康: ");
    Serial.println(systemHealthy ? "正常" : "异常");
    if(errorCode != 0) {
        Serial.print("错误代码: 0x");
        Serial.println(errorCode, HEX);
    }
    Serial.print("运行时间: ");
    Serial.print((millis() - systemStartTime) / 1000);
    Serial.println("秒");
    Serial.println("==================");
    Serial.println();
}
