/*
 * 温度报警测试程序
 * 模拟温度超过60°C时的系统响应
 */

#define MAX_TEMPERATURE 60

int8_t testTemperature = 25;
uint16_t errorCode = 0;
bool temperatureAlarmTriggered = false;

void setup() {
    Serial.begin(115200);
    Serial.println("=== 温度报警测试程序 ===");
    Serial.println("将模拟温度从25°C逐步升高到70°C");
    Serial.println("观察60°C时是否触发报警");
    Serial.println();
    delay(2000);
}

void loop() {
    // 每2秒升高5°C
    static unsigned long lastTempChange = 0;
    if(millis() - lastTempChange >= 2000) {
        lastTempChange = millis();
        
        if(testTemperature < 70) {
            testTemperature += 5;
        } else {
            testTemperature = 25; // 重置循环
            errorCode = 0;
            temperatureAlarmTriggered = false;
            Serial.println("\n=== 重新开始测试循环 ===\n");
        }
    }
    
    // 检查温度报警
    bool isOverTemp = (testTemperature > MAX_TEMPERATURE);
    
    // 显示当前状态
    Serial.print("当前温度: ");
    Serial.print(testTemperature);
    Serial.print("°C");
    
    if(isOverTemp) {
        Serial.print(" [超过限制 ");
        Serial.print(MAX_TEMPERATURE);
        Serial.print("°C]");
        
        if(!temperatureAlarmTriggered) {
            Serial.print(" → 🚨 触发温度报警!");
            errorCode = 0x0004;
            temperatureAlarmTriggered = true;
        }
    } else {
        Serial.print(" [正常范围]");
        if(temperatureAlarmTriggered && errorCode == 0x0004) {
            Serial.print(" → ✅ 温度恢复正常，清除报警");
            errorCode = 0;
            temperatureAlarmTriggered = false;
        }
    }
    
    Serial.print(" | 错误代码: 0x");
    Serial.print(errorCode, HEX);
    
    if(errorCode == 0x0004) {
        Serial.print(" (温度报警)");
    } else if(errorCode == 0) {
        Serial.print(" (正常)");
    }
    
    Serial.println();
    
    delay(500); // 每0.5秒更新一次显示
}
