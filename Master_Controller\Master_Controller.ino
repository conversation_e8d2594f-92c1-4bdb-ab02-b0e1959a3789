/*
 * 便携式电子对抗模拟训练系统 - 主控程序
 * 硬件平台: Arduino UNO R4 WiFi
 * 版本: v1.0.0
 * 作者: ECM Training System
 * 日期: 2024-01-16
 */

#include "Config.h"
#include "SystemManager.h"
#include "UIManager.h"
#include "TrainingManager.h"
#include "CommManager.h"
#include "ConfigManager.h"

// 全局对象实例
SystemManager systemMgr;
UIManager uiMgr;
TrainingManager trainMgr;
CommManager commMgr;
ConfigManager configMgr;

// 系统启动标志
bool systemInitialized = false;

void setup() {
    // 初始化串口调试
    Serial.begin(SERIAL_DEBUG_BAUD);
    delay(1000);
    
    Serial.println(F("================================="));
    Serial.println(F("电子对抗模拟训练系统 - 主控"));
    Serial.println(F("版本: ") + String(SYSTEM_VERSION));
    Serial.println(F("编译时间: ") + String(__DATE__) + " " + String(__TIME__));
    Serial.println(F("================================="));
    
    // 系统初始化序列
    Serial.println(F("开始系统初始化..."));
    
    // 1. 配置管理器初始化
    Serial.print(F("初始化配置管理器..."));
    if(configMgr.init()) {
        Serial.println(F("成功"));
    } else {
        Serial.println(F("失败"));
        systemError(ERROR_CONFIG_INIT);
        return;
    }
    
    // 2. 系统管理器初始化
    Serial.print(F("初始化系统管理器..."));
    if(systemMgr.init()) {
        Serial.println(F("成功"));
    } else {
        Serial.println(F("失败"));
        systemError(ERROR_SYSTEM_INIT);
        return;
    }
    
    // 3. 用户界面管理器初始化
    Serial.print(F("初始化用户界面..."));
    if(uiMgr.init()) {
        Serial.println(F("成功"));
    } else {
        Serial.println(F("失败"));
        systemError(ERROR_UI_INIT);
        return;
    }
    
    // 4. 训练管理器初始化
    Serial.print(F("初始化训练管理器..."));
    if(trainMgr.init()) {
        Serial.println(F("成功"));
    } else {
        Serial.println(F("失败"));
        systemError(ERROR_TRAINING_INIT);
        return;
    }
    
    // 5. 通信管理器初始化
    Serial.print(F("初始化通信管理器..."));
    if(commMgr.init()) {
        Serial.println(F("成功"));
    } else {
        Serial.println(F("失败"));
        systemError(ERROR_COMM_INIT);
        return;
    }
    
    // 显示启动画面
    uiMgr.showStartupScreen();
    
    // 系统自检
    Serial.println(F("执行系统自检..."));
    if(performSystemSelfTest()) {
        Serial.println(F("系统自检通过"));
        systemMgr.setState(SYS_READY);
        uiMgr.showMainMenu();
        systemInitialized = true;
    } else {
        Serial.println(F("系统自检失败"));
        systemError(ERROR_SELF_TEST);
        return;
    }
    
    Serial.println(F("系统初始化完成"));
    Serial.println(F("================================="));
}

void loop() {
    // 检查系统是否正常初始化
    if(!systemInitialized) {
        delay(1000);
        return;
    }
    
    // 主循环 - 10ms周期
    static unsigned long lastUpdate = 0;
    unsigned long currentTime = millis();
    
    if(currentTime - lastUpdate >= MAIN_LOOP_INTERVAL) {
        lastUpdate = currentTime;
        
        // 1. 系统状态更新
        systemMgr.update();
        
        // 2. 处理用户界面事件
        uiMgr.update();
        
        // 3. 训练管理更新
        trainMgr.update();
        
        // 4. 通信管理更新
        commMgr.update();
        
        // 5. 配置管理更新
        configMgr.update();
        
        // 6. 检查紧急停止
        checkEmergencyStop();
        
        // 7. 系统监控
        performSystemMonitoring();
    }
    
    // 处理串口调试命令
    handleSerialCommands();
}

// 系统自检函数
bool performSystemSelfTest() {
    bool testResult = true;
    
    Serial.println(F("  - 检查LED指示灯..."));
    if(!systemMgr.testLEDs()) {
        Serial.println(F("    LED测试失败"));
        testResult = false;
    }
    
    Serial.println(F("  - 检查TJC显示屏..."));
    if(!uiMgr.testDisplay()) {
        Serial.println(F("    显示屏测试失败"));
        testResult = false;
    }
    
    Serial.println(F("  - 检查配置存储..."));
    if(!configMgr.testEEPROM()) {
        Serial.println(F("    EEPROM测试失败"));
        testResult = false;
    }
    
    Serial.println(F("  - 检查串口通信..."));
    if(!commMgr.testSlaveConnection()) {
        Serial.println(F("    副控连接测试失败"));
        testResult = false;
    }

    Serial.println(F("  - 检查敌方信号模拟..."));
    if(!commMgr.startEnemySimulation()) {
        Serial.println(F("    敌方信号模拟启动失败"));
        // 这不是致命错误，继续运行
    }
    
    return testResult;
}

// 紧急停止检查
void checkEmergencyStop() {
    static bool lastEmergencyState = false;
    bool currentEmergencyState = digitalRead(EMERGENCY_STOP_PIN) == LOW;
    
    if(currentEmergencyState && !lastEmergencyState) {
        // 紧急停止按下
        Serial.println(F("紧急停止触发！"));
        systemMgr.emergencyStop();
        uiMgr.showEmergencyStop();
        trainMgr.stopAllTraining();
        commMgr.sendEmergencyStop();
    }
    
    lastEmergencyState = currentEmergencyState;
}

// 系统监控
void performSystemMonitoring() {
    static unsigned long lastMonitorTime = 0;
    unsigned long currentTime = millis();
    
    // 每秒执行一次系统监控
    if(currentTime - lastMonitorTime >= 1000) {
        lastMonitorTime = currentTime;
        
        // 检查系统状态
        SystemStatus status = systemMgr.getSystemStatus();
        
        // 检查电池电压
        if(status.batteryVoltage < MIN_BATTERY_VOLTAGE) {
            Serial.println(F("警告：电池电压过低"));
            uiMgr.showLowBatteryWarning();
        }
        
        // 检查温度
        if(status.temperature > MAX_TEMPERATURE) {
            Serial.println(F("警告：系统温度过高"));
            uiMgr.showOverTemperatureWarning();
        }
        
        // 检查通信状态
        if(!commMgr.isSlaveConnected() && systemMgr.getState() == SYS_TRAINING) {
            Serial.println(F("警告：副控串口连接丢失"));
            uiMgr.showCommLostWarning();
        }

        // 更新敌方信号模拟
        if(systemMgr.getState() == SYS_TRAINING) {
            // 敌方信号模拟在CommManager中自动更新
            EnemySignal enemyStatus = commMgr.getEnemySignalStatus();
            if(enemyStatus.active) {
                // 更新OLED显示干扰效果
                JammingEffectData effectData;
                effectData.targetSignalBefore = -45.0;
                effectData.targetSignalAfter = enemyStatus.rssi;
                effectData.packetLossRate = commMgr.getPacketLossRate();
                effectData.jammingEfficiency = commMgr.getJammingEfficiency();
                effectData.targetFrequency = enemyStatus.frequency;
                effectData.jammingActive = true;

                uiMgr.showJammingEffectOLED(effectData);
            }
        }
        
        // 更新状态显示
        uiMgr.updateSystemStatus(status);
    }
}

// 系统错误处理
void systemError(uint16_t errorCode) {
    Serial.print(F("系统错误: 0x"));
    Serial.println(errorCode, HEX);
    
    systemMgr.setState(SYS_ERROR);
    systemMgr.setErrorCode(errorCode);
    
    // LED错误指示
    systemMgr.setErrorLED(true);
    
    // 显示错误信息
    uiMgr.showError(errorCode);
    
    // 停止所有活动
    trainMgr.stopAllTraining();
    commMgr.disconnect();
}

// 串口调试命令处理
void handleSerialCommands() {
    if(Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if(command == "status") {
            printSystemStatus();
        } else if(command == "reset") {
            Serial.println(F("系统复位..."));
            systemMgr.softReset();
        } else if(command == "test") {
            Serial.println(F("执行系统测试..."));
            performSystemSelfTest();
        } else if(command.startsWith("led ")) {
            int ledNum = command.substring(4).toInt();
            systemMgr.toggleLED(ledNum);
        } else if(command == "help") {
            printHelpInfo();
        } else {
            Serial.println(F("未知命令，输入 'help' 查看帮助"));
        }
    }
}

// 打印系统状态
void printSystemStatus() {
    SystemStatus status = systemMgr.getSystemStatus();
    
    Serial.println(F("=== 系统状态 ==="));
    Serial.print(F("系统状态: "));
    Serial.println(systemMgr.getStateString());
    Serial.print(F("运行时间: "));
    Serial.print(status.uptime / 1000);
    Serial.println(F(" 秒"));
    Serial.print(F("电池电压: "));
    Serial.print(status.batteryVoltage);
    Serial.println(F(" mV"));
    Serial.print(F("系统温度: "));
    Serial.print(status.temperature);
    Serial.println(F(" °C"));
    Serial.print(F("副控连接: "));
    Serial.println(commMgr.isSlaveConnected() ? F("已连接") : F("未连接"));
    Serial.print(F("训练状态: "));
    Serial.println(trainMgr.getStatusString());
    Serial.println(F("================"));
}

// 打印帮助信息
void printHelpInfo() {
    Serial.println(F("=== 调试命令帮助 ==="));
    Serial.println(F("status  - 显示系统状态"));
    Serial.println(F("reset   - 软件复位"));
    Serial.println(F("test    - 执行系统测试"));
    Serial.println(F("led <n> - 切换LED状态"));
    Serial.println(F("help    - 显示此帮助"));
    Serial.println(F("==================="));
}
