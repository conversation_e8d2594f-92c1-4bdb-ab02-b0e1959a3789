# 代码实现规划

## 1. 开发环境配置

### 1.1 Arduino IDE设置
```
Arduino IDE版本: 2.0+
开发板配置:
- Arduino UNO R4 WiFi
- Arduino MEGA 2560

必需库文件:
- WiFi (内置)
- SPI (内置)
- Wire (内置)
- RF24 (NRF24L01通信)
- Adafruit_MCP4725 (DAC控制)
- Adafruit_SSD1306 (OLED显示)
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (数据序列化)
- EEPROM (参数存储)
```

### 1.2 项目目录结构
```
ElectronicCounterSystem/
├── Master_Controller/          # 主控代码
│   ├── Master_Controller.ino   # 主程序
│   ├── SystemManager.h/.cpp    # 系统管理
│   ├── CommManager.h/.cpp      # 通信管理
│   ├── UIManager.h/.cpp        # 界面管理
│   ├── TrainingManager.h/.cpp  # 训练管理
│   ├── WiFiManager.h/.cpp      # WiFi管理
│   └── Config.h                # 配置文件
├── Slave_Controller/           # 副控代码
│   ├── Slave_Controller.ino    # 主程序
│   ├── RFController.h/.cpp     # 射频控制
│   ├── SignalProcessor.h/.cpp  # 信号处理
│   ├── LocalDisplay.h/.cpp     # 本地显示
│   ├── CommInterface.h/.cpp    # 通信接口
│   └── Config.h                # 配置文件
├── Common/                     # 公共代码
│   ├── Protocol.h              # 通信协议
│   ├── DataStructures.h        # 数据结构
│   └── Utils.h/.cpp            # 工具函数
├── Libraries/                  # 自定义库
│   ├── VCOController/          # VCO控制库
│   ├── FilterController/       # 滤波器控制库
│   └── JammingAlgorithms/      # 干扰算法库
└── Documentation/              # 文档
    ├── API_Reference.md
    ├── Hardware_Manual.md
    └── User_Guide.md
```

## 2. 主控代码实现 (Master_Controller)

### 2.1 主程序框架
```cpp
// Master_Controller.ino
#include "SystemManager.h"
#include "CommManager.h"
#include "UIManager.h"
#include "TrainingManager.h"
#include "WiFiManager.h"
#include "Config.h"

// 全局对象
SystemManager systemMgr;
CommManager commMgr;
UIManager uiMgr;
TrainingManager trainMgr;
WiFiManager wifiMgr;

void setup() {
    Serial.begin(115200);
    
    // 初始化各模块
    systemMgr.init();
    commMgr.init();
    uiMgr.init();
    trainMgr.init();
    wifiMgr.init();
    
    // 显示启动信息
    uiMgr.showStartupScreen();
    
    // 建立与副控的连接
    commMgr.connectToSlave();
    
    Serial.println("Master Controller Initialized");
}

void loop() {
    // 主循环 - 10ms周期
    static unsigned long lastUpdate = 0;
    if(millis() - lastUpdate >= 10) {
        lastUpdate = millis();
        
        // 系统状态更新
        systemMgr.update();
        
        // 处理通信
        commMgr.update();
        
        // 更新界面
        uiMgr.update();
        
        // 训练管理
        trainMgr.update();
        
        // WiFi处理
        wifiMgr.update();
    }
}
```

### 2.2 系统管理模块
```cpp
// SystemManager.h
#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <Arduino.h>
#include "Config.h"

enum SystemState {
    SYS_INIT,
    SYS_READY,
    SYS_RUNNING,
    SYS_ERROR,
    SYS_SHUTDOWN
};

class SystemManager {
private:
    SystemState currentState;
    unsigned long systemUptime;
    uint16_t batteryVoltage;
    int8_t temperature;
    uint16_t errorCode;
    
public:
    void init();
    void update();
    void setState(SystemState state);
    SystemState getState();
    void handleError(uint16_t error);
    uint16_t getBatteryVoltage();
    int8_t getTemperature();
    unsigned long getUptime();
};

#endif
```

### 2.3 用户界面管理模块
```cpp
// UIManager.h
#ifndef UI_MANAGER_H
#define UI_MANAGER_H

#include <SoftwareSerial.h>
#include "Config.h"

class TJCDisplay {
private:
    SoftwareSerial tjcSerial;
    uint8_t currentPage;
    bool touchLocked;

public:
    void init();
    void sendCommand(String cmd);
    void switchPage(uint8_t pageId);
    void updateComponent(String component, String value);
    void lockTouch(bool locked);
    bool readTouchEvent(uint8_t& pageId, uint8_t& componentId);
};

class UIManager {
private:
    TJCDisplay display;
    uint8_t currentPage;
    bool touchLocked;

public:
    void init();
    void update();
    void handleTouchEvent(uint8_t pageId, uint8_t componentId);
    void handleCriticalButtons();
    void showTrainingProgress(uint8_t progress);
    void showSystemStatus(SystemStatus status);
    void lockInterface(bool locked);
};

#endif
```

## 3. 副控代码实现 (Slave_Controller)

### 3.1 主程序框架
```cpp
// Slave_Controller.ino
#include "RFController.h"
#include "SignalProcessor.h"
#include "LocalDisplay.h"
#include "CommInterface.h"
#include "Config.h"

// 全局对象
RFController rfCtrl;
SignalProcessor sigProc;
LocalDisplay display;
CommInterface commIntf;

void setup() {
    Serial.begin(115200);
    
    // 初始化各模块
    rfCtrl.init();
    sigProc.init();
    display.init();
    commIntf.init();
    
    // 显示启动信息
    display.showStartup();
    
    // 等待主控连接
    commIntf.waitForMaster();
    
    Serial.println("Slave Controller Initialized");
}

void loop() {
    // 主循环 - 5ms周期
    static unsigned long lastUpdate = 0;
    if(millis() - lastUpdate >= 5) {
        lastUpdate = millis();
        
        // 处理通信命令
        commIntf.processCommands();
        
        // 射频控制更新
        rfCtrl.update();
        
        // 信号处理
        sigProc.update();
        
        // 更新显示
        display.update();
        
        // 发送状态
        commIntf.sendStatus();
    }
}
```

### 3.2 本地显示模块 (自动轮询)
```cpp
// LocalDisplay.h
#ifndef LOCAL_DISPLAY_H
#define LOCAL_DISPLAY_H

#include <Adafruit_SSD1306.h>
#include <Wire.h>
#include "Config.h"

class LocalDisplay {
private:
    Adafruit_SSD1306 oled;
    uint8_t currentPage;
    unsigned long lastPageSwitch;
    unsigned long pageDisplayTime;
    bool alertMode;
    uint16_t alertCode;

    // 页面常量
    static const uint8_t PAGE_RF_STATUS = 0;
    static const uint8_t PAGE_SPECTRUM = 1;
    static const uint8_t PAGE_SYSTEM_MONITOR = 2;
    static const uint8_t PAGE_TRAINING_STATS = 3;
    static const uint8_t TOTAL_PAGES = 4;
    static const unsigned long PAGE_DURATION = 5000; // 5秒

public:
    void init();
    void update();  // 主更新函数，处理自动轮询

    // 各页面显示函数
    void showRFStatus(uint32_t freq, float power, String mode, float temp);
    void showSpectrum(float* spectrumData, uint8_t dataPoints);
    void showSystemMonitor(uint8_t cpuUsage, uint16_t ramUsage,
                          uint8_t batteryLevel, float temperature);
    void showTrainingStats(unsigned long duration, uint32_t packetsSent,
                          float successRate);
    void showAlert(uint16_t errorCode, String message);

    // 控制函数
    void setAlertMode(bool alert, uint16_t code = 0);
    void nextPage();
    void resetPageTimer();
};

#endif
```

## 4. 库文件依赖管理

### 4.1 必需库文件列表
```cpp
// libraries.txt - Arduino IDE库管理器安装
RF24 by TMRh20                    // NRF24L01通信
Adafruit MCP4725 by Adafruit      // DAC控制
Adafruit SSD1306 by Adafruit      // OLED显示
Adafruit GFX Library by Adafruit  // 图形库
ArduinoJson by Benoit Blanchon    // JSON处理
WiFi by Arduino                   // WiFi功能 (内置)
SPI by Arduino                    // SPI通信 (内置)
Wire by Arduino                   // I2C通信 (内置)
EEPROM by Arduino                 // EEPROM存储 (内置)
```

### 4.2 库版本兼容性
```cpp
// Config.h - 版本检查
#if !defined(RF24_VERSION) || RF24_VERSION < 1008000
#error "RF24 library version 1.8.0 or higher required"
#endif

#if !defined(ADAFRUIT_MCP4725_VERSION) || ADAFRUIT_MCP4725_VERSION < 200
#error "Adafruit MCP4725 library version 2.0.0 or higher required"
#endif
```

## 5. 配置文件设计

### 5.1 主配置文件
```cpp
// Config.h
#ifndef CONFIG_H
#define CONFIG_H

// 系统版本
#define SYSTEM_VERSION "1.0.0"
#define BUILD_DATE __DATE__

// 硬件配置
#define MASTER_BOARD_UNO_R4
#define SLAVE_BOARD_MEGA_2560

// 主控引脚配置
#define TJC_SERIAL_RX 0
#define TJC_SERIAL_TX 1
#define EMERGENCY_STOP_PIN 8
#define POWER_SWITCH_PIN 9
#define STATUS_LED_POWER 5
#define STATUS_LED_COMM 6
#define STATUS_LED_ERROR 7

// 通信配置
#define NRF24_CE_PIN 3
#define NRF24_CSN_PIN 4
#define NRF24_CHANNEL 76
#define NRF24_DATARATE RF24_2MBPS
#define COMM_TIMEOUT_MS 1000
#define HEARTBEAT_INTERVAL_MS 1000

// 射频配置
#define RF_MIN_FREQ 2400
#define RF_MAX_FREQ 2500
#define RF_MIN_POWER 0
#define RF_MAX_POWER 20
#define DAC_I2C_ADDRESS 0x60

// 显示配置
#define OLED_I2C_ADDRESS 0x3C
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define TJC_SERIAL_BAUD 9600
#define OLED_PAGE_DURATION 5000  // 5秒轮询间隔

// 触摸屏页面定义
#define PAGE_MAIN_MENU 0
#define PAGE_TRAINING_SELECT 1
#define PAGE_PARAMETER_SETUP 2
#define PAGE_TRAINING_RUNNING 3
#define PAGE_DATA_ANALYSIS 4
#define PAGE_SYSTEM_SETTINGS 5

// 安全配置
#define MAX_TEMPERATURE 60
#define MIN_BATTERY_VOLTAGE 6500  // mV
#define TOUCH_LOCK_TIMEOUT 30000  // 30秒无操作自动锁定

// 调试配置
#define DEBUG_MODE 1
#define SERIAL_DEBUG_BAUD 115200

#endif
```

## 6. 编译和部署

### 6.1 编译配置
```cpp
// 编译选项 (在Arduino IDE中设置)
主控 (UNO R4 WiFi):
- Board: "Arduino UNO R4 WiFi"
- Optimize: "Small (-Os) (standard)"
- Debug: "Off"

副控 (MEGA 2560):
- Board: "Arduino Mega or Mega 2560"
- Processor: "ATmega2560 (Mega 2560)"
- Optimize: "Small (-Os) (standard)"
```

### 6.2 内存使用优化
```cpp
// 内存优化技巧
// 1. 使用PROGMEM存储常量字符串
const char PROGMEM str_system_ready[] = "System Ready";

// 2. 使用F()宏减少RAM使用
Serial.println(F("Debug message"));

// 3. 避免String类，使用char数组
char buffer[32];
snprintf(buffer, sizeof(buffer), "Freq: %lu MHz", frequency);

// 4. 使用位域节省结构体空间
struct SystemFlags {
    uint8_t transmitting : 1;
    uint8_t connected : 1;
    uint8_t error : 1;
    uint8_t reserved : 5;
};
```

## 7. 测试和调试

### 7.1 单元测试框架
```cpp
// TestFramework.h
#ifndef TEST_FRAMEWORK_H
#define TEST_FRAMEWORK_H

class TestFramework {
private:
    static int testsPassed;
    static int testsFailed;
    
public:
    static void runAllTests();
    static void testVCOController();
    static void testCommManager();
    static void testSignalProcessor();
    static void assert(bool condition, const char* message);
    static void reportResults();
};

// 测试宏
#define TEST_ASSERT(condition, message) \
    TestFramework::assert(condition, message)

#endif
```

### 7.2 调试工具
```cpp
// Debug.h
#ifndef DEBUG_H
#define DEBUG_H

#if DEBUG_MODE
#define DEBUG_PRINT(x) Serial.print(x)
#define DEBUG_PRINTLN(x) Serial.println(x)
#define DEBUG_PRINTF(fmt, ...) Serial.printf(fmt, __VA_ARGS__)
#else
#define DEBUG_PRINT(x)
#define DEBUG_PRINTLN(x)
#define DEBUG_PRINTF(fmt, ...)
#endif

// 性能监控
class PerformanceMonitor {
public:
    static void startTiming(const char* name);
    static void endTiming(const char* name);
    static void printStats();
};

#endif
```

## 8. 部署和维护

### 8.1 固件更新机制
```cpp
// OTA更新支持 (仅主控UNO R4 WiFi)
#include <WiFiOTA.h>

void setupOTA() {
    WiFiOTA.begin("ECM_System", "password");
    WiFiOTA.onStart([]() {
        Serial.println("OTA Update Started");
    });
    WiFiOTA.onEnd([]() {
        Serial.println("OTA Update Completed");
    });
}
```

### 8.2 配置备份和恢复
```cpp
// ConfigManager.h
class ConfigManager {
public:
    static void saveConfig();
    static void loadConfig();
    static void resetToDefaults();
    static void backupConfig();
    static void restoreConfig();
};
```
