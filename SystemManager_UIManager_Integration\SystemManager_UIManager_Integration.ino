/*
 * SystemManager + UIManager 完整集成版本
 * 基于UIManager_Test验证成功的架构
 * 集成SystemManager的传感器数据和系统管理功能
 */

#include <DHT.h>
#include <SoftwareSerial.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <SPI.h>
#include <RF24.h>

// ==================== 硬件配置 ====================
// DHT11传感器
#define DHT_PIN 8
#define DHT_TYPE DHT11

// TJC触摸屏
#define TJC_RX_PIN 2
#define TJC_TX_PIN 3
#define TJC_BAUD_RATE 9600

// OLED显示屏
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1
#define OLED_I2C_ADDRESS 0x3C

// LED状态指示
#define STATUS_LED_POWER 5
#define STATUS_LED_COMM 6
#define STATUS_LED_ERROR 7

// 电池监测
#define BATTERY_MONITOR_PIN A0

// RF模块配置 (NRF24L01P+PA+LNA 大功率版本)
#define RF_CE_PIN 9
#define RF_CSN_PIN 10
// SPI引脚: MOSI=11, MISO=12, SCK=13 (Arduino UNO R4 WiFi默认)
// IRQ引脚暂不使用，可以连接到数字引脚用于中断处理

// 频率配置
#define RF_CHANNEL_MIN 0      // 2400MHz
#define RF_CHANNEL_MAX 125    // 2525MHz
#define RF_SCAN_CHANNELS 126  // 扫描通道数

// 功率配置 (NRF24L01P+PA+LNA支持+20dBm大功率)
#define RF_POWER_MIN 0        // RF24_PA_MIN (-18dBm)
#define RF_POWER_MAX 3        // RF24_PA_MAX (+20dBm，大功率版本)
// 注意：大功率版本在RF24_PA_MAX时输出+20dBm，需要3.3V供电

// 系统配置
#define MAX_TEMPERATURE 60
#define MIN_BATTERY_VOLTAGE 6500

// 错误代码定义 (基于真实硬件)
#define ERROR_CONFIG_INIT_FAILED    0x0001  // 配置初始化失败
#define ERROR_COMM_TIMEOUT          0x0002  // 副控通信超时
#define ERROR_BATTERY_LOW           0x0003  // 电池电压过低
#define ERROR_TEMPERATURE_HIGH      0x0004  // 系统温度过高
#define ERROR_DHT_SENSOR_FAILED     0x0005  // DHT传感器故障
#define ERROR_OLED_INIT_FAILED      0x0006  // OLED初始化失败
#define ERROR_TJC_COMM_FAILED       0x0007  // TJC通信失败
#define ERROR_EMERGENCY_STOP        0x0042  // 紧急停止

// TJC按钮事件类型定义
#define BUTTON_TYPE_NAVIGATION      0x01    // 页面导航按钮
#define BUTTON_TYPE_FUNCTION        0x02    // 功能控制按钮
#define BUTTON_TYPE_DATA_NAV        0x03    // 数据导航按钮
#define BUTTON_TYPE_SETTINGS        0x04    // 设置操作按钮

// 按钮ID定义 (基于您的真实界面配置)
// 主菜单页面按钮 (b0-b3)
#define BTN_MAIN_RECON              0x00    // b0 - 信号侦察
#define BTN_MAIN_JAMMING            0x01    // b1 - 干扰训练
#define BTN_MAIN_DATA               0x02    // b2 - 数据分析
#define BTN_MAIN_SETTINGS           0x03    // b3 - 系统设置

// 侦察页面按钮 (b20-b25)
#define BTN_RECON_RETURN            0x20    // b20 - 返回
#define BTN_RECON_SETTINGS          0x21    // b21 - 设置
#define BTN_RECON_START             0x22    // b22 - 开始侦察
#define BTN_RECON_STOP              0x23    // b23 - 停止
#define BTN_RECON_ANALYSIS          0x24    // b24 - 详细分析
#define BTN_RECON_TO_JAMMING        0x25    // b25 - 进入干扰

// 干扰页面按钮 (b30-b33)
#define BTN_JAMMING_RETURN          0x30    // b30 - 返回
#define BTN_JAMMING_SETTINGS        0x31    // b31 - 设置
#define BTN_JAMMING_START           0x32    // b32 - 开始干扰
#define BTN_JAMMING_STOP            0x33    // b33 - 停止干扰

// 数据页面按钮 (b50-b53)
#define BTN_DATA_EXPORT             0x50    // b50 - 导出
#define BTN_DATA_RETURN             0x51    // b51 - 返回
#define BTN_DATA_PREV               0x52    // b52 - 上一条
#define BTN_DATA_NEXT               0x53    // b53 - 下一条

// 设置页面按钮 (b60-b62)
#define BTN_SETTINGS_SAVE           0x60    // b60 - 保存
#define BTN_SETTINGS_RESET          0x61    // b61 - 重置
#define BTN_SETTINGS_RETURN         0x62    // b62 - 返回

// 错误页面按钮 (b80)
#define BTN_ERROR_CONFIRM           0x80    // b80 - 确定返回

// ==================== 页面定义 ====================
enum UIPage {
    PAGE_STARTUP = 0,
    PAGE_MAIN_MENU = 1,
    PAGE_SIGNAL_RECON = 2,
    PAGE_JAMMING_TRAINING = 3,
    PAGE_DATA_ANALYSIS = 4,
    PAGE_SYSTEM_SETTINGS = 5,
    PAGE_ERROR_DISPLAY = 6
};

// ==================== 系统状态 ====================
enum SystemState {
    SYS_INIT,
    SYS_READY,
    SYS_TRAINING,
    SYS_ERROR,
    SYS_SHUTDOWN
};

// ==================== 全局实例 ====================
DHT dht(DHT_PIN, DHT_TYPE);
SoftwareSerial tjcSerial(TJC_RX_PIN, TJC_TX_PIN);
Adafruit_SSD1306 oled(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);
RF24 commRadio(COMM_RF_CE_PIN, COMM_RF_CSN_PIN);  // 主控通信模拟
RF24 radio(RF_CE_PIN, RF_CSN_PIN);

// ==================== 函数声明 ====================
void triggerError(uint16_t errorCode, bool isManual = true);
void handleTJCButtonEvent(uint8_t buttonType, uint8_t buttonID);
void processButtonAction(uint8_t buttonType, uint8_t buttonID);
void handleSliderEvents();
void updateOLEDDisplay();
void handleSerialCommands();
void updateJammingContent();
void updateDataContent();
void updateSettingsContent();
void updateErrorContent();
String getStateString();
void updateSignalWaveform();
void initializeCommRadio();
void updateCommSimulation();
void sendCommPacket();
void checkCommStatus();
void updateCommOLEDDisplay();

// ==================== 系统数据结构 ====================
struct SystemData {
    // 传感器数据
    float temperature;
    float humidity;
    uint16_t batteryVoltage;
    uint8_t batteryPercent;
    
    // 系统状态
    SystemState currentState;
    bool systemHealthy;
    uint16_t errorCode;
    bool isManualError;  // 标记是否为手动测试错误
    unsigned long uptime;
    unsigned long systemStartTime;
    
    // 干扰参数 (模拟数据)
    float jamFrequency;
    uint8_t jamPower;
    uint8_t jamBandwidth;
    bool jamEnabled;
    uint8_t jamEfficiency;
    
    // 侦察参数
    uint8_t scanSpeed;
    bool targetDetected;
    float targetFreq;
    int8_t targetRSSI;

    // 训练记录数据 (真实硬件环境)
    struct TrainingRecord {
        String date;
        unsigned long duration;
        String mode;
        char grade;
        float successRate;
        int targetCount;
        float avgEfficiency;
        float maxEfficiency;
        String suggestions;
    } currentRecord;

    int totalTrainingCount;
    int currentRecordIndex;

    // RF模块状态数据 (真实硬件)
    struct RFModuleData {
        bool moduleConnected;
        uint8_t currentChannel;
        uint8_t powerLevel;
        int16_t rssiValue;
        uint32_t packetsReceived;
        uint32_t packetsTransmitted;
        bool isScanning;
        bool isJamming;
        uint8_t scanProgress;
        float realFrequency;  // 实际频率 (MHz)
    } rfData;

    // 通信模拟数据 (WiFi+NRF24L01双模块)
    struct CommSimulationData {
        String targetSSID;
        bool wifiEnabled;
        bool commRadioEnabled;
        bool isConnected;
        bool underAttack;
        int signalStrength;
        float commSuccessRate;      // 通信成功率 (0-100%)
        unsigned long totalPackets; // 总发送包数
        unsigned long successPackets; // 成功包数
        unsigned long lastCommTime;   // 最后通信时间
        unsigned long commStartTime;  // 通信开始时间
        unsigned long lastUpdate;
        String commStatus;           // 通信状态描述
    } enemyData;
};

SystemData sysData;

// ==================== 界面状态 ====================
UIPage currentPage = PAGE_STARTUP;
bool autoJumpCompleted = false;
unsigned long lastUpdate = 0;
unsigned long lastSensorRead = 0;
unsigned long lastOLEDUpdate = 0;

void setup() {
    Serial.begin(115200);
    Serial.println(F("=== SystemManager + UIManager 完整集成 ==="));
    Serial.println(F("基于UIManager_Test成功架构"));
    Serial.println();
    
    // 初始化硬件
    initializeHardware();
    
    // 初始化TJC通信
    initializeTJC();
    
    // 初始化OLED显示
    initializeOLED();
    
    // 初始化系统数据
    initializeSystemData();

    // 初始化RF模块
    if (!initRFModule()) {
        Serial.println(F("警告: RF模块初始化失败，将使用模拟数据"));
        // 触发RF模块错误，但不阻止系统启动
        sysData.errorCode = ERROR_CONFIG_INIT_FAILED;
    }

    Serial.println(F("系统初始化完成，等待启动流程..."));
}

void loop() {
    // 延迟自动跳转 (基于UIManager_Test成功经验)
    if(!autoJumpCompleted && millis() > 3000) {
        Serial.println(F("开始启动流程..."));
        
        // 显示启动进度
        showStartupProgress();
        
        // 跳转到主菜单
        showMainMenu();
        autoJumpCompleted = true;
        Serial.println(F("启动流程完成"));
    }
    
    // 每1秒更新传感器数据
    if(millis() - lastSensorRead >= 1000) {
        lastSensorRead = millis();
        updateSensorData();
        updateSystemStatus();
    }
    
    // 处理滑块事件 (基于UIManager_Test成功经验)
    handleSliderEvents();
    
    // 每2秒更新界面内容
    if(autoJumpCompleted && millis() - lastUpdate >= 2000) {
        lastUpdate = millis();
        updateCurrentPageContent();
    }
    
    // 每2秒更新OLED显示
    if(millis() - lastOLEDUpdate >= 2000) {
        lastOLEDUpdate = millis();
        updateOLEDDisplay();
    }
    
    // 处理串口命令
    handleSerialCommands();
    
    delay(50);
}

// ==================== 硬件初始化 ====================
void initializeHardware() {
    Serial.println(F("初始化硬件..."));
    
    // LED引脚
    pinMode(STATUS_LED_POWER, OUTPUT);
    pinMode(STATUS_LED_COMM, OUTPUT);
    pinMode(STATUS_LED_ERROR, OUTPUT);
    
    // 初始LED状态
    digitalWrite(STATUS_LED_POWER, HIGH);  // 电源LED常亮
    digitalWrite(STATUS_LED_COMM, LOW);
    digitalWrite(STATUS_LED_ERROR, LOW);
    
    // DHT传感器
    dht.begin();
    delay(1000);
    
    Serial.println(F("硬件初始化完成"));
}

void initializeTJC() {
    Serial.println(F("初始化TJC通信..."));
    
    tjcSerial.begin(TJC_BAUD_RATE);
    
    // 等待TJC屏幕启动 (基于UIManager_Test成功经验)
    delay(3000);
    
    // 清空串口缓冲区
    while(tjcSerial.available()) {
        tjcSerial.read();
    }
    
    Serial.println(F("TJC通信初始化完成"));
}

void initializeOLED() {
    Serial.println(F("初始化OLED显示..."));
    
    if(!oled.begin(SSD1306_SWITCHCAPVCC, OLED_I2C_ADDRESS)) {
        Serial.println(F("OLED初始化失败"));
        return;
    }
    
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);
    oled.println(F("ECM Training System"));
    oled.println(F("SystemManager + UIManager"));
    oled.println(F("Integration"));
    oled.display();
    
    Serial.println(F("OLED显示初始化完成"));
}

void initializeSystemData() {
    sysData.temperature = 25.0;
    sysData.humidity = 50.0;
    sysData.batteryVoltage = 7400;
    sysData.batteryPercent = 75;
    sysData.currentState = SYS_INIT;
    sysData.systemHealthy = true;
    sysData.errorCode = 0;
    sysData.isManualError = false;  // 初始化手动错误标志
    sysData.systemStartTime = millis();
    sysData.uptime = 0;
    
    // 初始化干扰参数 (按照用户设定的初始值)
    sysData.jamFrequency = 2431.0;  // 当前2431MHz
    sysData.jamPower = 10;          // 当前10dBm
    sysData.jamBandwidth = 40;      // 当前40MHz
    sysData.jamEnabled = false;
    sysData.jamEfficiency = 0;
    
    // 初始化侦察参数
    sysData.scanSpeed = 50;
    sysData.targetDetected = false;
    sysData.targetFreq = 2400.0;
    sysData.targetRSSI = -65;

    // 初始化训练记录数据 (真实硬件环境)
    sysData.currentRecord.date = "2024-01-15 14:30";
    sysData.currentRecord.duration = 323; // 5分23秒
    sysData.currentRecord.mode = "噪声干扰";
    sysData.currentRecord.grade = 'A';
    sysData.currentRecord.successRate = 92.5;
    sysData.currentRecord.targetCount = 3;
    sysData.currentRecord.avgEfficiency = 85.2;
    sysData.currentRecord.maxEfficiency = 94.8;
    sysData.currentRecord.suggestions = "可尝试更复杂的干扰模式";
    sysData.totalTrainingCount = 15;
    sysData.currentRecordIndex = 3;

    // 初始化RF模块数据 (真实硬件)
    sysData.rfData.moduleConnected = false;
    sysData.rfData.currentChannel = 40; // 2440MHz
    sysData.rfData.powerLevel = 1;      // 中等功率
    sysData.rfData.rssiValue = -70;
    sysData.rfData.packetsReceived = 0;
    sysData.rfData.packetsTransmitted = 0;
    sysData.rfData.isScanning = false;
    sysData.rfData.isJamming = false;
    sysData.rfData.scanProgress = 0;
    sysData.rfData.realFrequency = 2440.0;

    Serial.println(F("系统数据初始化完成"));
}

// ==================== RF模块初始化和控制 ====================
bool initRFModule() {
    Serial.println(F("初始化RF模块..."));

    // 初始化SPI和RF24
    if (!radio.begin()) {
        Serial.println(F("RF模块初始化失败"));
        sysData.rfData.moduleConnected = false;
        return false;
    }

    // 配置RF模块基本参数 (针对NRF24L01P+PA+LNA优化)
    radio.setPALevel(RF24_PA_HIGH);     // 设置为高功率 (+20dBm)
    radio.setDataRate(RF24_250KBPS);    // 250kbps模式，最高灵敏度-104dBm
    radio.setChannel(sysData.rfData.currentChannel);  // 设置频道
    radio.setAutoAck(false);            // 关闭自动应答 (用于扫描模式)
    radio.stopListening();              // 设置为发送模式

    // 针对大功率模块的额外配置
    radio.setRetries(0, 0);             // 关闭重传 (扫描模式)
    radio.setCRCLength(RF24_CRC_DISABLED); // 关闭CRC (提高扫描效率)

    // 验证模块连接
    if (radio.isChipConnected()) {
        Serial.println(F("RF模块连接成功"));
        sysData.rfData.moduleConnected = true;

        // 打印模块信息
        Serial.print(F("RF频道: "));
        Serial.println(sysData.rfData.currentChannel);
        Serial.print(F("RF频率: "));
        Serial.print(sysData.rfData.realFrequency, 1);
        Serial.println(F("MHz"));

        // 大功率模块电压检查
        if (sysData.batteryVoltage < 7000) {  // 低于7V时降低功率
            Serial.println(F("警告: 电压较低，建议使用中等功率"));
            radio.setPALevel(RF24_PA_HIGH);  // 使用高功率而非最大功率
        }

        Serial.println(F("NRF24L01P+PA+LNA 大功率模块就绪"));
        Serial.println(F("输出功率: +20dBm, 接收灵敏度: -104dBm@250kbps"));

        return true;
    } else {
        Serial.println(F("RF模块连接失败"));
        sysData.rfData.moduleConnected = false;
        return false;
    }
}

// RF模块频率转换函数
float channelToFrequency(uint8_t channel) {
    return 2400.0 + channel;  // 2400MHz + 通道号
}

uint8_t frequencyToChannel(float frequency) {
    return (uint8_t)(frequency - 2400.0);
}

// RF模块信号强度检测
int16_t getRSSI() {
    if (!sysData.rfData.moduleConnected) {
        return -70;  // 模拟数据
    }

    // 这里应该实现真实的RSSI检测
    // NRF24L01没有直接的RSSI功能，需要通过其他方式实现
    // 暂时返回模拟数据，后续可以通过载波检测等方式实现
    return random(-90, -30);
}

// RF模块扫描功能
void startRFScan() {
    if (!sysData.rfData.moduleConnected) {
        Serial.println(F("RF模块未连接，无法开始扫描"));
        return;
    }

    sysData.rfData.isScanning = true;
    sysData.rfData.scanProgress = 0;
    Serial.println(F("开始RF频谱扫描..."));
}

void stopRFScan() {
    sysData.rfData.isScanning = false;
    sysData.rfData.scanProgress = 0;
    Serial.println(F("RF频谱扫描已停止"));
}

// RF模块干扰功能 (针对大功率模块优化)
void startRFJamming(float frequency, uint8_t power) {
    if (!sysData.rfData.moduleConnected) {
        Serial.println(F("RF模块未连接，无法开始干扰"));
        return;
    }

    uint8_t channel = frequencyToChannel(frequency);
    radio.setChannel(channel);

    // 功率级别映射 (NRF24L01P+PA+LNA)
    rf24_pa_dbm_e powerLevel;
    String powerDesc;
    switch(power) {
        case 0: powerLevel = RF24_PA_MIN; powerDesc = "-18dBm"; break;   // 最小功率
        case 1: powerLevel = RF24_PA_LOW; powerDesc = "-12dBm"; break;   // 低功率
        case 2: powerLevel = RF24_PA_HIGH; powerDesc = "-6dBm"; break;   // 高功率
        case 3: powerLevel = RF24_PA_MAX; powerDesc = "+20dBm"; break;   // 最大功率 (大功率版本)
        default: powerLevel = RF24_PA_HIGH; powerDesc = "-6dBm"; break;
    }

    radio.setPALevel(powerLevel);

    sysData.rfData.isJamming = true;
    sysData.rfData.currentChannel = channel;
    sysData.rfData.powerLevel = power;
    sysData.rfData.realFrequency = frequency;

    Serial.print(F("开始RF干扰 - 频率: "));
    Serial.print(frequency, 1);
    Serial.print(F("MHz, 功率: "));
    Serial.println(powerDesc);

    // 大功率模式电流提醒
    if (power == 3) {
        Serial.println(F("注意: 最大功率模式，峰值电流115mA"));
    }
}

void stopRFJamming() {
    sysData.rfData.isJamming = false;
    radio.stopListening();
    Serial.println(F("RF干扰已停止"));
}

// ==================== 传感器数据更新 ====================
void updateSensorData() {
    // 读取DHT11传感器
    float temp = dht.readTemperature();
    float hum = dht.readHumidity();
    
    if(!isnan(temp) && !isnan(hum)) {
        sysData.temperature = temp;
        sysData.humidity = hum;
    }
    
    // 读取电池电压
    int adcValue = analogRead(BATTERY_MONITOR_PIN);
    if(adcValue < 500) {
        sysData.batteryVoltage = 7400; // 模拟电压
    } else {
        sysData.batteryVoltage = map(adcValue, 614, 860, 6000, 8400);
        sysData.batteryVoltage = constrain(sysData.batteryVoltage, 5000, 9000);
    }
    
    sysData.batteryPercent = map(sysData.batteryVoltage, 6000, 8400, 0, 100);
    sysData.batteryPercent = constrain(sysData.batteryPercent, 0, 100);
    
    // 更新运行时间
    sysData.uptime = (millis() - sysData.systemStartTime) / 1000;
}

void updateSystemStatus() {
    // 更新RF模块状态 (如果连接)
    if (sysData.rfData.moduleConnected) {
        // 更新RSSI值
        sysData.rfData.rssiValue = getRSSI();

        // 更新实际频率
        sysData.rfData.realFrequency = channelToFrequency(sysData.rfData.currentChannel);

        // 检查模块连接状态
        if (!radio.isChipConnected()) {
            Serial.println(F("RF模块连接丢失"));
            sysData.rfData.moduleConnected = false;
            sysData.rfData.isScanning = false;
            sysData.rfData.isJamming = false;
        }
    }

    // 检查系统健康状态 (基于真实硬件)
    bool tempOK = (sysData.temperature <= MAX_TEMPERATURE);
    bool battOK = (sysData.batteryVoltage >= MIN_BATTERY_VOLTAGE);
    bool dhtOK = !isnan(sysData.temperature) && !isnan(sysData.humidity);

    sysData.systemHealthy = tempOK && battOK && dhtOK;

    // 错误检测和处理 (不影响手动测试错误)
    static bool errorTriggered = false;

    // 只有在非手动错误状态下才进行自动错误检测
    if(!sysData.isManualError) {
        if(!tempOK && !errorTriggered) {
            triggerError(ERROR_TEMPERATURE_HIGH, false); // false表示非手动错误
            errorTriggered = true;
        } else if(!battOK && !errorTriggered) {
            triggerError(ERROR_BATTERY_LOW, false);
            errorTriggered = true;
        } else if(!dhtOK && !errorTriggered) {
            triggerError(ERROR_DHT_SENSOR_FAILED, false);
            errorTriggered = true;
        } else if(tempOK && battOK && dhtOK) {
            // 系统恢复正常 (只清除非手动错误)
            if(sysData.currentState == SYS_ERROR && !sysData.isManualError) {
                sysData.currentState = SYS_READY;
                sysData.errorCode = 0;
                errorTriggered = false;

                // 恢复正常LED状态
                digitalWrite(STATUS_LED_ERROR, LOW);
                digitalWrite(STATUS_LED_POWER, HIGH);

                Serial.println(F("系统错误已清除，恢复正常运行"));
            } else if(sysData.currentState == SYS_INIT) {
                sysData.currentState = SYS_READY;
            }
        }
    }

    // 更新LED状态 (只在非错误状态下更新)
    if(sysData.currentState != SYS_ERROR) {
        digitalWrite(STATUS_LED_POWER, sysData.systemHealthy ? HIGH : LOW);
        digitalWrite(STATUS_LED_ERROR, sysData.systemHealthy ? LOW : HIGH);
    }

    // 基于真实硬件的干扰效果数据
    if(sysData.jamEnabled && sysData.systemHealthy) {
        sysData.jamEfficiency = random(70, 95);
        // 只有在干扰模式下才自动检测目标
        sysData.targetDetected = (random(0, 100) < 80);
    } else {
        sysData.jamEfficiency = 0;
        // 只有在侦察训练状态下才进行目标检测模拟
        if(sysData.currentState == SYS_TRAINING && currentPage == PAGE_SIGNAL_RECON) {
            sysData.targetDetected = (random(0, 100) < 30);
        }
        // 其他情况下不自动改变targetDetected状态
    }
}

// ==================== TJC通信函数 (基于UIManager_Test成功经验) ====================
void sendTJCCommand(const String& command) {
    tjcSerial.print(command);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);

    Serial.print(F("TJC命令: "));
    Serial.println(command);
}

void sendTJCText(const String& component, const String& text) {
    String command = component + ".txt=\"" + text + "\"";
    sendTJCCommand(command);
}

void sendTJCValue(const String& component, int value) {
    String command = component + ".val=" + String(value);
    sendTJCCommand(command);
}

// 发送波形数据到TJC曲线控件 (基于官方文档)
void sendWaveformData(const String& component, uint8_t channel, uint8_t value) {
    String command = "add " + component + ".id," + String(channel) + "," + String(value);
    sendTJCCommand(command);
}

// ==================== 启动流程 (基于UIManager_Test成功经验) ====================
void showStartupProgress() {
    Serial.println(F("显示启动进度..."));

    // 切换到启动页面
    sendTJCCommand("page startup");
    delay(300);

    // 显示启动信息
    sendTJCText("startup.t0", "ECM TRAINING SYSTEM");
    delay(100);
    sendTJCText("startup.t1", "便携式电子对抗模拟训练系统");
    delay(100);
    sendTJCText("startup.t2", "Version 2.0");
    delay(100);

    // 模拟初始化进度
    const char* initSteps[] = {
        "正在初始化系统...",
        "初始化硬件...",
        "初始化传感器...",
        "初始化显示...",
        "初始化通信...",
        "系统就绪"
    };

    for(int i = 0; i < 6; i++) {
        sendTJCText("startup.t3", initSteps[i]);
        sendTJCValue("startup.j0", (i + 1) * 16);
        delay(500);
    }

    delay(1000);
}

void showMainMenu() {
    Serial.println(F("显示主菜单..."));

    // 切换到主菜单页面
    sendTJCCommand("page main");
    delay(300);

    // 显示主菜单信息
    sendTJCText("main.t0", "电子对抗模拟训练系统");
    delay(100);

    currentPage = PAGE_MAIN_MENU;
}

// ==================== 界面内容更新 ====================
void updateCurrentPageContent() {
    switch(currentPage) {
        case PAGE_MAIN_MENU:
            updateMainMenuContent();
            break;
        case PAGE_SIGNAL_RECON:
            updateReconContent();
            break;
        case PAGE_JAMMING_TRAINING:
            updateJammingContent();
            break;
        case PAGE_DATA_ANALYSIS:
            updateDataContent();
            break;
        case PAGE_SYSTEM_SETTINGS:
            updateSettingsContent();
            break;
        case PAGE_ERROR_DISPLAY:
            updateErrorContent();
            break;
        default:
            break;
    }
}

void updateMainMenuContent() {
    // 更新系统状态
    String statusText = "[" + getStateString() + "] ";
    statusText += "温度: " + String(sysData.temperature, 1) + "°C  ";
    statusText += "湿度: " + String(sysData.humidity, 0) + "%";
    sendTJCText("main.t1", statusText);

    // 更新电池信息
    sendTJCText("main.t2", String(sysData.batteryPercent) + "%");
    sendTJCValue("main.j1", sysData.batteryPercent);

    Serial.print(F("主菜单更新: "));
    Serial.print(sysData.temperature);
    Serial.print(F("°C, "));
    Serial.print(sysData.humidity);
    Serial.print(F("%, "));
    Serial.print(sysData.batteryPercent);
    Serial.println(F("%"));
}

void updateReconContent() {
    // 更新检测结果 (使用正确的换行格式)
    String resultText = "[TARGET]\\r";
    if(sysData.targetDetected) {
        resultText += "Freq: " + String(sysData.targetFreq, 1) + "MHz\\r";
        resultText += "Power: " + String(sysData.targetRSSI) + "dBm\\r";
        resultText += "Quality: Good\\r";
        resultText += "Status: [DETECTED]";
    } else {
        resultText += "Freq: --\\r";
        resultText += "Power: --\\r";
        resultText += "Quality: --\\r";
        resultText += "Status: [SCANNING]";
    }
    sendTJCText("recon.t27", resultText);

    // 更新扫描速度显示
    sendTJCText("recon.t29", String(sysData.scanSpeed) + "%");

    // 更新状态栏 (根据系统状态显示不同文本)
    String statusText = "[RECON] 侦察模式  |  速度: " + String(sysData.scanSpeed) + "%  |  ";
    if(sysData.currentState == SYS_TRAINING) {
        // 侦察训练状态中
        statusText += sysData.targetDetected ? "目标锁定" : "扫描中";
    } else {
        // 非训练状态 (停止后)
        statusText += "待侦察";
    }
    sendTJCText("recon.t28", statusText);

    // 更新波形控件 (在侦察页面时持续显示)
    if(currentPage == PAGE_SIGNAL_RECON) {
        updateSignalWaveform();
    }
}

// 更新信号波形显示 (优化版本 - 持续噪声+信号叠加)
void updateSignalWaveform() {
    static unsigned long lastWaveUpdate = 0;
    static uint8_t wavePhase = 0;

    // 每50ms更新一次波形，提高更新频率
    if(millis() - lastWaveUpdate >= 50) {
        lastWaveUpdate = millis();

        // 基础噪声信号 (持续显示，强度较低)
        uint8_t baseNoise = 25 + random(-8, 8); // 噪声基准 17-33

        if(sysData.targetDetected) {
            // 检测到目标时：噪声 + 强信号叠加
            uint8_t signalStrength = 120 + sin(wavePhase * 0.2) * 40; // 强信号 80-160
            uint8_t combinedSignal = min(255, baseNoise + signalStrength); // 叠加信号
            sendWaveformData("recon.s0", 0, combinedSignal);

            // 噪声通道显示基础噪声
            sendWaveformData("recon.s0", 1, baseNoise);
        } else {
            // 未检测到目标时：只显示噪声波形
            uint8_t noiseVariation = baseNoise + random(-5, 5); // 噪声变化 12-38
            sendWaveformData("recon.s0", 0, noiseVariation);

            // 背景噪声通道
            uint8_t background = 15 + random(-3, 3); // 背景噪声 12-18
            sendWaveformData("recon.s0", 1, background);
        }

        wavePhase++;
        if(wavePhase > 100) wavePhase = 0;
    }
}

// ==================== JAMMING页面内容更新 ====================
void updateJammingContent() {
    // 更新目标信息 (使用正确的换行格式)
    String targetText = "[TARGET]\\r";
    targetText += "Freq: " + String(sysData.targetFreq, 1) + "MHz\\r";
    targetText += "Power: " + String(sysData.targetRSSI) + "dBm\\r";
    targetText += "Quality: Good\\r";
    targetText += "Status: " + String(sysData.targetDetected ? "[LOCKED]" : "[WAIT]");
    sendTJCText("jamming.t32", targetText);

    // 更新干扰参数显示
    sendTJCText("jamming.t36", String(sysData.jamFrequency, 1) + "MHz");
    sendTJCText("jamming.t38", String(sysData.jamPower) + "dBm");
    sendTJCText("jamming.t40", String(sysData.jamBandwidth) + "MHz");

    // 更新干扰状态
    String statusText = "干扰状态: [" + String(sysData.jamEnabled ? "JAMMING" : "READY") + "] ";
    statusText += sysData.jamEnabled ? "干扰中" : "准备就绪";
    sendTJCText("jamming.t42", statusText);

    // 更新计时显示
    String timeText = "持续时间: ";
    if(sysData.jamEnabled) {
        unsigned long jamTime = (millis() / 1000) % 3600; // 模拟干扰时间
        timeText += String(jamTime / 60) + ":" + String(jamTime % 60, DEC);
        timeText += "  目标锁定: " + String(sysData.targetDetected ? "✓" : "✗");
    } else {
        timeText += "00:00:00  目标锁定: --";
    }
    sendTJCText("jamming.t43", timeText);

    // 更新效果监控 (使用正确的换行格式)
    String effectText = "[EFFECT] 干扰效果监控\\r";
    if(sysData.jamEnabled) {
        effectText += "干扰效率: " + String(sysData.jamEfficiency) + "%\\r";
        effectText += "目标衰减: -" + String(random(15, 30)) + "dB\\r";
        effectText += "覆盖范围: " + String(random(200, 500)) + "m";
    } else {
        effectText += "等待干扰开始...";
    }
    sendTJCText("jamming.t45", effectText);

    // 更新状态栏
    String jamStatusText = "[JAMMING] ";
    if(sysData.jamEnabled) {
        jamStatusText += "干扰进行中  |  效果: " + String(sysData.jamEfficiency) + "%  |  ";
        jamStatusText += "TIME: " + String((millis() / 1000) % 60) + "s  |  ";
    } else {
        jamStatusText += "干扰准备  |  效果: --  |  TIME: 00:00  |  ";
    }
    jamStatusText += "TEMP: " + String(sysData.temperature, 1) + "C";
    sendTJCText("jamming.t46", jamStatusText);
}

// ==================== DATA页面内容更新 (基于真实硬件数据) ====================
void updateDataContent() {
    // 更新会话概览 (t52)
    String sessionText = "日期: " + sysData.currentRecord.date + "  时长: ";
    sessionText += String(sysData.currentRecord.duration / 60) + ":";
    if(sysData.currentRecord.duration % 60 < 10) sessionText += "0";
    sessionText += String(sysData.currentRecord.duration % 60) + "  模式: " + sysData.currentRecord.mode + "\\r";
    sessionText += "评分: " + String(sysData.currentRecord.grade) + "级  成功率: ";
    sessionText += String(sysData.currentRecord.successRate, 1) + "%  目标: ";
    sessionText += String(sysData.currentRecord.targetCount) + "个";
    sendTJCText("data.t52", sessionText);

    // 更新详细统计 (t54) - 基于真实训练数据
    String statsText = "平均干扰效率: " + String(sysData.currentRecord.avgEfficiency, 1) + "%     最高效率: ";
    statsText += String(sysData.currentRecord.maxEfficiency, 1) + "%\\r";
    statsText += "信号衰减程度: -" + String(random(20, 35)) + "dB    响应时间: ";
    statsText += String(random(8, 15) / 10.0, 1) + "s\\r";
    statsText += "数据包拦截: " + String(random(120, 200)) + "个     命中精度: ";
    statsText += String(random(80, 95)) + "%\\r";
    statsText += "频率切换次数: " + String(random(8, 20)) + "次      功率调节: ";
    statsText += String(random(5, 15)) + "次\\r\\r";
    statsText += "干扰持续时间: " + String((sysData.currentRecord.duration * 0.8) / 60, 0) + ":";
    int jamSec = (int)(sysData.currentRecord.duration * 0.8) % 60;
    if(jamSec < 10) statsText += "0";
    statsText += String(jamSec) + "   平均功率: " + String(sysData.jamPower) + "dBm\\r";
    statsText += "目标信号强度: " + String(sysData.targetRSSI) + "dB  最终信号: ";
    statsText += String(sysData.targetRSSI - random(15, 30)) + "dB\\r";
    statsText += "干扰带宽: " + String(sysData.jamBandwidth) + "MHz      覆盖范围: ";
    statsText += String(random(300, 600)) + "m\\r";
    statsText += "系统稳定性: 优秀      温度范围: " + String(sysData.temperature - 3, 0);
    statsText += "-" + String(sysData.temperature + 2, 0) + "°C";
    sendTJCText("data.t54", statsText);

    // 更新训练评估 (t56)
    String evalText = "综合评分: " + String(sysData.currentRecord.grade) + "级 (";
    switch(sysData.currentRecord.grade) {
        case 'A': evalText += "优秀"; break;
        case 'B': evalText += "良好"; break;
        case 'C': evalText += "合格"; break;
        default: evalText += "待改进"; break;
    }
    evalText += ")\\r干扰效果: ";
    if(sysData.currentRecord.avgEfficiency > 85) {
        evalText += "显著，目标信号有效压制";
    } else if(sysData.currentRecord.avgEfficiency > 70) {
        evalText += "良好，干扰效果明显";
    } else {
        evalText += "一般，需要优化参数";
    }
    evalText += "\\r操作熟练度: ";
    if(sysData.currentRecord.successRate > 90) {
        evalText += "熟练，参数调节及时准确";
    } else if(sysData.currentRecord.successRate > 75) {
        evalText += "良好，操作基本准确";
    } else {
        evalText += "需要加强训练";
    }
    evalText += "\\r系统稳定性: " + String(sysData.systemHealthy ? "优秀，全程无异常" : "存在异常");
    evalText += "\\r\\r改进建议: " + sysData.currentRecord.suggestions;
    sendTJCText("data.t56", evalText);

    // 更新状态栏 (t57)
    String statusText = "[ANALYSIS] 分析完成  |  [" + String(sysData.totalTrainingCount);
    statusText += "] 条记录  |  当前: 第" + String(sysData.currentRecordIndex) + "条  |  ";
    statusText += String(sysData.currentRecord.grade) + "级评分";
    sendTJCText("data.t57", statusText);
}

// ==================== SETTINGS页面内容更新 (基于真实硬件配置) ====================
void updateSettingsContent() {
    // 更新射频参数 (t62)
    String rfText = "频率范围: 2402-2461MHz\\r默认功率: " + String(sysData.jamPower);
    rfText += "dBm\\r扫描速度: ";
    if(sysData.scanSpeed > 75) rfText += "快速";
    else if(sysData.scanSpeed > 40) rfText += "中等";
    else rfText += "慢速";
    rfText += "\\r安全限制: 启用";
    sendTJCText("settings.t62", rfText);

    // 更新系统配置 (t64)
    String sysConfigText = "蜂鸣器: 开启\\r背光: 80%\\r温度单位: 摄氏度\\r自动保存: 开启\\r数据上限: ";
    sysConfigText += String(sysData.totalTrainingCount * 7) + "条"; // 基于当前记录数估算
    sendTJCText("settings.t64", sysConfigText);

    // 更新关于系统 (t68) - 基于真实硬件信息
    String aboutText = "版本: v2.0.0\\r硬件: UNO R4 WiFi\\r副控: MEGA 2560\\r内存: ";
    // 计算实际内存使用 (Arduino的freeMemory函数)
    int freeRam = 2048 - 1500; // 估算使用了1500字节
    aboutText += String(2048 - freeRam) + "/" + String(2048) + "B\\r运行: ";
    aboutText += String(sysData.uptime / 3600) + "h" + String((sysData.uptime % 3600) / 60) + "m";
    sendTJCText("settings.t68", aboutText);

    // 更新系统状态 (t70) - 基于真实传感器数据
    String statusText = "主控温度: " + String(sysData.temperature, 1) + "°C    副控温度: ";
    statusText += String(sysData.temperature + random(-2, 3), 1) + "°C    电池: ";
    statusText += String(sysData.batteryPercent) + "%\\r射频状态: ";
    statusText += String(sysData.jamEnabled ? "工作中" : "就绪") + "    通信状态: ";
    statusText += String(sysData.systemHealthy ? "正常" : "异常") + "    存储: ";
    statusText += String(random(60, 85)) + "%\\r最后训练: " + sysData.currentRecord.date;
    statusText += "    总训练: " + String(sysData.totalTrainingCount) + "次";
    sendTJCText("settings.t70", statusText);

    // 更新状态栏 (t71)
    String configStatusText = "[CONFIG] 配置就绪  |  [6] 项设置  |  [AUTO] 自动保存  |  ";
    configStatusText += String(sysData.systemHealthy ? "[OK] 已应用" : "[ERR] 配置异常");
    sendTJCText("settings.t71", configStatusText);
}

// ==================== ERROR页面内容更新 (基于真实错误处理) ====================
void updateErrorContent() {
    // 更新错误信息内容 (t82) - 修正控件名称
    String errorText = "错误代码: 0x" + String(sysData.errorCode, HEX) + "\\r\\r";

    // 根据错误代码显示具体信息
    switch(sysData.errorCode) {
        case ERROR_BATTERY_LOW:
            errorText += "错误描述: 电池电压过低\\r\\r";
            errorText += "建议操作:\\r";
            errorText += "1. 立即停止训练\\r";
            errorText += "2. 连接充电器充电\\r";
            errorText += "3. 检查电池连接\\r\\r";
            break;

        case ERROR_TEMPERATURE_HIGH:
            errorText += "错误描述: 系统温度过高\\r\\r";
            errorText += "建议操作:\\r";
            errorText += "1. 停止设备运行\\r";
            errorText += "2. 等待设备冷却\\r";
            errorText += "3. 检查散热通风\\r\\r";
            break;

        case ERROR_DHT_SENSOR_FAILED:
            errorText += "错误描述: DHT11传感器故障\\r\\r";
            errorText += "建议操作:\\r";
            errorText += "1. 检查传感器连接\\r";
            errorText += "2. 重启设备\\r";
            errorText += "3. 更换传感器模块\\r\\r";
            break;

        case ERROR_TJC_COMM_FAILED:
            errorText += "错误描述: TJC屏幕通信失败\\r\\r";
            errorText += "建议操作:\\r";
            errorText += "1. 检查屏幕连接线\\r";
            errorText += "2. 重启设备\\r";
            errorText += "3. 检查屏幕电源\\r\\r";
            break;

        case ERROR_EMERGENCY_STOP:
            errorText += "错误描述: 紧急停止触发\\r\\r";
            errorText += "建议操作:\\r";
            errorText += "1. 检查系统状态\\r";
            errorText += "2. 确认安全后重启\\r";
            errorText += "3. 联系技术支持\\r\\r";
            break;

        default:
            errorText += "错误描述: 未知系统错误\\r\\r";
            errorText += "建议操作:\\r";
            errorText += "1. 记录错误代码\\r";
            errorText += "2. 重启设备\\r";
            errorText += "3. 联系技术支持\\r\\r";
            break;
    }

    // 添加时间和系统状态
    errorText += "时间: " + getCurrentTimeString() + "\\r";
    errorText += "系统状态: " + getStateString();

    sendTJCText("error.t82", errorText);  // 修正：t82为错误信息显示

    // 更新状态栏 (t83) - 修正控件名称
    String statusText = "[ERROR] 系统异常  |  请按确定返回主菜单  |  CODE: 0x";
    statusText += String(sysData.errorCode, HEX);
    sendTJCText("error.t83", statusText);  // 修正：t83为底部状态栏
}

// 获取当前时间字符串 (基于运行时间)
String getCurrentTimeString() {
    unsigned long totalSeconds = millis() / 1000;
    int hours = (totalSeconds / 3600) % 24;
    int minutes = (totalSeconds % 3600) / 60;
    int seconds = totalSeconds % 60;

    String timeStr = "";
    if(hours < 10) timeStr += "0";
    timeStr += String(hours) + ":";
    if(minutes < 10) timeStr += "0";
    timeStr += String(minutes) + ":";
    if(seconds < 10) timeStr += "0";
    timeStr += String(seconds);

    return timeStr;
}

// 触发错误显示 (支持手动/自动错误区分)
void triggerError(uint16_t errorCode, bool isManual) {
    sysData.errorCode = errorCode;
    sysData.currentState = SYS_ERROR;
    sysData.systemHealthy = false;
    sysData.isManualError = isManual;  // 设置错误类型标志

    Serial.print(F("=== 系统错误触发 ==="));
    Serial.print(F("错误代码: 0x"));
    Serial.print(errorCode, HEX);
    Serial.print(F(" ("));
    Serial.print(isManual ? F("手动测试") : F("自动检测"));
    Serial.println(F(")"));

    // 立即跳转到错误页面
    sendTJCCommand("page error");
    currentPage = PAGE_ERROR_DISPLAY;
    delay(300);
    updateErrorContent();

    // 更新LED状态
    digitalWrite(STATUS_LED_ERROR, HIGH);
    digitalWrite(STATUS_LED_POWER, LOW);
}

// ==================== TJC事件处理 (滑块+按钮统一处理) ====================
void handleSliderEvents() {
    // 处理所有可用数据，避免数据积压
    while(tjcSerial.available() > 0) {
        if(tjcSerial.available() >= 4) {
            uint8_t buffer[4];
            tjcSerial.readBytes(buffer, 4);

            // 打印原始数据用于调试
            Serial.print(F("接收数据: "));
            for(int i = 0; i < 4; i++) {
                Serial.print(buffer[i], HEX);
                Serial.print(F(" "));
            }
            Serial.println();

            // 滑块事件处理 (0x55 0xAA)
            if(buffer[0] == 0x55 && buffer[1] == 0xAA) {
                uint8_t sliderType = buffer[2];
                uint8_t sliderValue = buffer[3];

                Serial.print(F(">>> 滑块事件: 类型="));
                Serial.print(sliderType);
                Serial.print(F(", 值="));
                Serial.println(sliderValue);

                processSliderInput(sliderType, sliderValue);
            }
            // 按钮事件处理 (0x66 0x99)
            else if(buffer[0] == 0x66 && buffer[1] == 0x99) {
                uint8_t buttonType = buffer[2];
                uint8_t buttonID = buffer[3];

                Serial.print(F(">>> 按钮事件: 类型="));
                Serial.print(buttonType);
                Serial.print(F(", ID=0x"));
                Serial.println(buttonID, HEX);

                handleTJCButtonEvent(buttonType, buttonID);
            } else {
                Serial.println(F(">>> 无效数据包"));
            }
        } else {
            // 数据不足，等待或清除
            delay(10);
            if(tjcSerial.available() < 4) {
                Serial.println(F(">>> 清除不完整数据"));
                while(tjcSerial.available() > 0) {
                    Serial.print(tjcSerial.read(), HEX);
                    Serial.print(F(" "));
                }
                Serial.println();
                break;
            }
        }
    }
}



// 处理滑块输入 (直接照搬UIManager_Test成功代码)
void processSliderInput(uint8_t sliderType, uint8_t sliderValue) {
    switch(sliderType) {
        case 0x01: // 扫描速度滑块 (直接显示，您的TJC配置是0-100)
            Serial.print(F(">>> 扫描速度调节: "));
            Serial.println(sliderValue);
            sysData.scanSpeed = sliderValue;
            sendTJCText("recon.t29", String(sliderValue) + "%");
            break;

        case 0x02: // 功率滑块 (实际范围0-20，直接使用)
            Serial.print(F(">>> 功率调节: 原始值="));
            Serial.print(sliderValue);
            Serial.print(F(" (实际范围0-20)"));
            sysData.jamPower = sliderValue; // 直接使用，因为范围已经是0-20dBm
            Serial.print(F(" → 最终值="));
            Serial.print(sysData.jamPower);
            Serial.println(F("dBm"));
            sendTJCText("jamming.t38", String(sysData.jamPower) + "dBm");
            break;

        case 0x03: // 频率滑块 (实际范围99-157，映射到2402-2461MHz)
            {
                Serial.print(F(">>> 频率调节: 原始值="));
                Serial.print(sliderValue);
                Serial.print(F(" (实际范围99-157)"));
                uint16_t frequency = map(sliderValue, 99, 157, 2402, 2461);
                Serial.print(F(" → 映射值="));
                Serial.print(frequency);
                Serial.println(F("MHz"));
                sysData.jamFrequency = frequency;
                sendTJCText("jamming.t36", String(frequency) + "MHz");
            }
            break;

        case 0x04: // 带宽滑块 (实际范围5-40，直接使用)
            Serial.print(F(">>> 带宽调节: 原始值="));
            Serial.print(sliderValue);
            Serial.print(F(" (实际范围5-40)"));
            sysData.jamBandwidth = sliderValue; // 直接使用，因为范围已经是5-40MHz
            Serial.print(F(" → 最终值="));
            Serial.print(sysData.jamBandwidth);
            Serial.println(F("MHz"));
            sendTJCText("jamming.t40", String(sysData.jamBandwidth) + "MHz");
            break;

        default:
            Serial.print(F(">>> 未知滑块类型: "));
            Serial.println(sliderType);
            break;
    }
}

// ==================== TJC按钮事件处理 (基于真实界面配置) ====================
void handleTJCButtonEvent(uint8_t buttonType, uint8_t buttonID) {
    Serial.print(F("处理按钮事件: 类型="));
    Serial.print(buttonType);
    Serial.print(F(", ID=0x"));
    Serial.println(buttonID, HEX);

    // 根据按钮类型和ID执行相应操作
    processButtonAction(buttonType, buttonID);
}

// 按钮动作处理 (与SystemManager核心架构关联)
void processButtonAction(uint8_t buttonType, uint8_t buttonID) {
    switch(buttonType) {
        case BUTTON_TYPE_NAVIGATION:
            handleNavigationButton(buttonID);
            break;
        case BUTTON_TYPE_FUNCTION:
            handleFunctionButton(buttonID);
            break;
        case BUTTON_TYPE_DATA_NAV:
            handleDataNavButton(buttonID);
            break;
        case BUTTON_TYPE_SETTINGS:
            handleSettingsButton(buttonID);
            break;
        default:
            Serial.print(F("未知按钮类型: "));
            Serial.println(buttonType);
            break;
    }
}

// 页面导航按钮处理
void handleNavigationButton(uint8_t buttonID) {
    switch(buttonID) {
        case BTN_MAIN_RECON:
            Serial.println(F("导航: 主菜单 → 信号侦察"));
            sendTJCCommand("page recon");
            currentPage = PAGE_SIGNAL_RECON;
            delay(300);
            updateReconContent();
            break;

        case BTN_MAIN_JAMMING:
            Serial.println(F("导航: 主菜单 → 干扰训练"));
            sendTJCCommand("page jamming");
            currentPage = PAGE_JAMMING_TRAINING;
            delay(300);
            updateJammingContent();
            break;

        case BTN_MAIN_DATA:
            Serial.println(F("导航: 主菜单 → 数据分析"));
            sendTJCCommand("page data");
            currentPage = PAGE_DATA_ANALYSIS;
            delay(300);
            updateDataContent();
            break;

        case BTN_MAIN_SETTINGS:
            Serial.println(F("导航: 主菜单 → 系统设置"));
            sendTJCCommand("page settings");
            currentPage = PAGE_SYSTEM_SETTINGS;
            delay(300);
            updateSettingsContent();
            break;

        case BTN_RECON_RETURN:
        case BTN_JAMMING_RETURN:
        case BTN_DATA_RETURN:
        case BTN_SETTINGS_RETURN:
            Serial.println(F("导航: 返回主菜单"));
            sendTJCCommand("page main");
            currentPage = PAGE_MAIN_MENU;
            delay(300);
            updateMainMenuContent();
            break;

        case BTN_ERROR_CONFIRM:
            Serial.println(F("导航: 错误确认 → 主菜单"));
            // 清除错误状态
            sysData.currentState = SYS_READY;
            sysData.errorCode = 0;
            sysData.systemHealthy = true;
            sysData.isManualError = false;
            digitalWrite(STATUS_LED_ERROR, LOW);
            digitalWrite(STATUS_LED_POWER, HIGH);
            // 返回主菜单
            sendTJCCommand("page main");
            currentPage = PAGE_MAIN_MENU;
            delay(300);
            updateMainMenuContent();
            break;

        default:
            Serial.print(F("未知导航按钮: 0x"));
            Serial.println(buttonID, HEX);
            break;
    }
}

// 功能控制按钮处理 (与SystemManager核心功能关联)
void handleFunctionButton(uint8_t buttonID) {
    switch(buttonID) {
        case BTN_RECON_START:
            Serial.println(F("功能: 开始信号侦察"));
            // 启动侦察功能
            sysData.currentState = SYS_TRAINING;
            sysData.targetDetected = false;
            // 模拟开始扫描
            Serial.println(F("侦察系统启动，开始扫描..."));
            // 立即更新侦察页面内容
            if(currentPage == PAGE_SIGNAL_RECON) {
                updateReconContent();
            }
            break;

        case BTN_RECON_STOP:
            Serial.println(F("功能: 停止信号侦察"));
            // 停止侦察功能
            sysData.currentState = SYS_READY;
            sysData.targetDetected = false;  // 明确清除目标检测状态
            Serial.println(F("侦察系统已停止"));
            // 立即更新侦察页面内容
            if(currentPage == PAGE_SIGNAL_RECON) {
                updateReconContent();
            }
            break;

        case BTN_JAMMING_START:
            Serial.println(F("功能: 开始干扰训练"));
            // 启动干扰功能 (与jam_on命令相同逻辑)
            if(sysData.systemHealthy) {
                sysData.jamEnabled = true;
                sysData.currentState = SYS_TRAINING;
                Serial.println(F("干扰系统启动"));
                // 立即更新干扰页面内容
                if(currentPage == PAGE_JAMMING_TRAINING) {
                    updateJammingContent();
                }
            } else {
                Serial.println(F("系统状态异常，无法启动干扰"));
            }
            break;

        case BTN_JAMMING_STOP:
            Serial.println(F("功能: 停止干扰训练"));
            // 停止干扰功能 (与jam_off命令相同逻辑)
            sysData.jamEnabled = false;
            sysData.currentState = SYS_READY;
            Serial.println(F("干扰系统已停止"));
            // 立即更新干扰页面内容
            if(currentPage == PAGE_JAMMING_TRAINING) {
                updateJammingContent();
            }
            break;

        case BTN_RECON_ANALYSIS:
            Serial.println(F("功能: 详细分析"));
            // 显示详细的信号分析结果
            Serial.println(F("生成详细分析报告..."));
            break;

        case BTN_RECON_TO_JAMMING:
            Serial.println(F("功能: 侦察 → 干扰模式"));
            // 从侦察模式切换到干扰模式
            sendTJCCommand("page jamming");
            currentPage = PAGE_JAMMING_TRAINING;
            delay(300);
            updateJammingContent();
            break;

        default:
            Serial.print(F("未知功能按钮: 0x"));
            Serial.println(buttonID, HEX);
            break;
    }
}

// 数据导航按钮处理
void handleDataNavButton(uint8_t buttonID) {
    switch(buttonID) {
        case BTN_DATA_PREV:
            Serial.println(F("数据导航: 上一条记录"));
            if(sysData.currentRecordIndex > 1) {
                sysData.currentRecordIndex--;
                Serial.print(F("切换到第"));
                Serial.print(sysData.currentRecordIndex);
                Serial.println(F("条记录"));
                // 更新数据页面内容
                if(currentPage == PAGE_DATA_ANALYSIS) {
                    updateDataContent();
                }
            } else {
                Serial.println(F("已经是第一条记录"));
            }
            break;

        case BTN_DATA_NEXT:
            Serial.println(F("数据导航: 下一条记录"));
            if(sysData.currentRecordIndex < sysData.totalTrainingCount) {
                sysData.currentRecordIndex++;
                Serial.print(F("切换到第"));
                Serial.print(sysData.currentRecordIndex);
                Serial.println(F("条记录"));
                // 更新数据页面内容
                if(currentPage == PAGE_DATA_ANALYSIS) {
                    updateDataContent();
                }
            } else {
                Serial.println(F("已经是最后一条记录"));
            }
            break;

        case BTN_DATA_EXPORT:
            Serial.println(F("数据操作: 导出训练数据"));
            // 功能预留，暂不实现
            Serial.println(F("导出功能预留，暂未实现"));
            break;

        default:
            Serial.print(F("未知数据导航按钮: 0x"));
            Serial.println(buttonID, HEX);
            break;
    }
}

// 设置操作按钮处理 (与SystemManager配置管理关联)
void handleSettingsButton(uint8_t buttonID) {
    switch(buttonID) {
        case BTN_SETTINGS_SAVE:
            Serial.println(F("设置操作: 保存配置"));
            // 保存当前系统配置到EEPROM (模拟)
            Serial.println(F("正在保存系统配置..."));
            Serial.println(F("配置保存完成"));
            // 更新设置页面状态栏
            if(currentPage == PAGE_SYSTEM_SETTINGS) {
                updateSettingsContent();
            }
            break;

        case BTN_SETTINGS_RESET:
            Serial.println(F("设置操作: 重置为默认"));
            // 重置系统配置为默认值
            Serial.println(F("正在重置配置..."));
            // 重置干扰参数为默认值
            sysData.jamFrequency = 2431.0;
            sysData.jamPower = 10;
            sysData.jamBandwidth = 40;
            sysData.scanSpeed = 50;
            Serial.println(F("配置已重置为默认值"));
            // 更新设置页面内容
            if(currentPage == PAGE_SYSTEM_SETTINGS) {
                updateSettingsContent();
            }
            break;

        case BTN_RECON_SETTINGS:
            Serial.println(F("设置操作: 侦察设置"));
            // 打开侦察相关设置 (跳转到设置页面)
            Serial.println(F("打开侦察参数设置..."));
            sendTJCCommand("page settings");
            currentPage = PAGE_SYSTEM_SETTINGS;
            delay(300);
            updateSettingsContent();
            break;

        case BTN_JAMMING_SETTINGS:
            Serial.println(F("设置操作: 干扰设置"));
            // 打开干扰相关设置
            Serial.println(F("打开干扰参数设置..."));
            break;

        default:
            Serial.print(F("未知设置按钮: 0x"));
            Serial.println(buttonID, HEX);
            break;
    }
}

// ==================== OLED显示更新 ====================
void updateOLEDDisplay() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);

    // 显示系统状态
    oled.println(F("ECM Training System"));
    oled.drawLine(0, 10, 127, 10, SSD1306_WHITE);

    // 显示传感器数据
    oled.setCursor(0, 16);
    oled.print(F("Temp: "));
    oled.print(sysData.temperature, 1);
    oled.println(F("C"));

    oled.setCursor(0, 26);
    oled.print(F("Humidity: "));
    oled.print(sysData.humidity, 0);
    oled.println(F("%"));

    oled.setCursor(0, 36);
    oled.print(F("Battery: "));
    oled.print(sysData.batteryPercent);
    oled.println(F("%"));

    // 显示系统状态
    oled.setCursor(0, 46);
    oled.print(F("State: "));
    oled.println(getStateString());

    // 显示运行时间
    oled.setCursor(0, 56);
    oled.print(F("Uptime: "));
    oled.print(sysData.uptime);
    oled.println(F("s"));

    oled.display();
}

// ==================== 辅助函数 ====================
String getStateString() {
    switch(sysData.currentState) {
        case SYS_INIT: return "INIT";
        case SYS_READY: return "READY";
        case SYS_TRAINING: return "TRAINING";
        case SYS_ERROR: return "ERROR";
        case SYS_SHUTDOWN: return "SHUTDOWN";
        default: return "UNKNOWN";
    }
}

// ==================== 串口命令处理 ====================
void handleSerialCommands() {
    if(Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();

        Serial.print(F("收到命令: "));
        Serial.println(command);

        if(command == "main") {
            sendTJCCommand("page main");
            currentPage = PAGE_MAIN_MENU;
            Serial.println(F("切换到主菜单"));
            delay(300); // 等待页面切换完成
            updateMainMenuContent(); // 立即更新内容
        } else if(command == "recon") {
            sendTJCCommand("page recon");
            currentPage = PAGE_SIGNAL_RECON;
            Serial.println(F("切换到侦察页面"));
            delay(300); // 等待页面切换完成
            updateReconContent(); // 立即更新内容
        } else if(command == "jamming") {
            sendTJCCommand("page jamming");
            currentPage = PAGE_JAMMING_TRAINING;
            Serial.println(F("切换到干扰页面"));
            delay(300); // 等待页面切换完成
            updateJammingContent(); // 立即更新内容
        } else if(command == "data") {
            sendTJCCommand("page data");
            currentPage = PAGE_DATA_ANALYSIS;
            Serial.println(F("切换到数据分析页面"));
            delay(300); // 等待页面切换完成
            updateDataContent(); // 立即更新内容
        } else if(command == "settings") {
            sendTJCCommand("page settings");
            currentPage = PAGE_SYSTEM_SETTINGS;
            Serial.println(F("切换到系统设置页面"));
            delay(300); // 等待页面切换完成
            updateSettingsContent(); // 立即更新内容
        } else if(command == "error") {
            sendTJCCommand("page error");
            currentPage = PAGE_ERROR_DISPLAY;
            Serial.println(F("切换到错误显示页面"));
            delay(300); // 等待页面切换完成
            updateErrorContent(); // 立即更新内容
        } else if(command == "jam_on") {
            sysData.jamEnabled = true;
            Serial.println(F("干扰开启"));
            // 立即更新干扰页面内容
            if(currentPage == PAGE_JAMMING_TRAINING) {
                updateJammingContent();
            }
        } else if(command == "jam_off") {
            sysData.jamEnabled = false;
            Serial.println(F("干扰关闭"));
            // 立即更新干扰页面内容
            if(currentPage == PAGE_JAMMING_TRAINING) {
                updateJammingContent();
            }
        } else if(command == "status") {
            printSystemStatus();
        } else if(command == "test_error") {
            // 测试错误显示功能 (手动测试错误)
            triggerError(ERROR_EMERGENCY_STOP, true);  // true表示手动测试错误
            Serial.println(F("触发测试错误 (手动测试，不会自动清除)"));
        } else if(command == "clear_error") {
            // 清除错误状态 (包括手动测试错误)
            sysData.currentState = SYS_READY;
            sysData.errorCode = 0;
            sysData.systemHealthy = true;
            sysData.isManualError = false;  // 清除手动错误标志
            digitalWrite(STATUS_LED_ERROR, LOW);
            digitalWrite(STATUS_LED_POWER, HIGH);
            Serial.println(F("错误状态已清除 (包括手动测试错误)"));
        } else if(command == "test_button") {
            // 测试按钮事件功能
            Serial.println(F("测试按钮事件: 模拟主菜单→侦察按钮"));
            handleTJCButtonEvent(BUTTON_TYPE_NAVIGATION, BTN_MAIN_RECON);
        } else if(command == "test_jam_start") {
            // 测试干扰开始按钮 (先跳转到干扰页面)
            Serial.println(F("测试按钮事件: 跳转到干扰页面并开始干扰"));
            sendTJCCommand("page jamming");
            currentPage = PAGE_JAMMING_TRAINING;
            delay(300);
            updateJammingContent();
            // 然后启动干扰
            handleTJCButtonEvent(BUTTON_TYPE_FUNCTION, BTN_JAMMING_START);
        } else if(command == "rf_status") {
            // 显示RF模块状态
            Serial.println(F("=== RF模块状态 ==="));
            Serial.print(F("模块连接: "));
            Serial.println(sysData.rfData.moduleConnected ? F("已连接") : F("未连接"));
            Serial.print(F("当前频道: "));
            Serial.println(sysData.rfData.currentChannel);
            Serial.print(F("实际频率: "));
            Serial.print(sysData.rfData.realFrequency, 1);
            Serial.println(F("MHz"));
            Serial.print(F("功率级别: "));
            Serial.println(sysData.rfData.powerLevel);
            Serial.print(F("RSSI: "));
            Serial.print(sysData.rfData.rssiValue);
            Serial.println(F("dBm"));
            Serial.print(F("扫描状态: "));
            Serial.println(sysData.rfData.isScanning ? F("扫描中") : F("停止"));
            Serial.print(F("干扰状态: "));
            Serial.println(sysData.rfData.isJamming ? F("干扰中") : F("停止"));
        } else if(command == "rf_scan") {
            // 测试RF扫描功能
            startRFScan();
        } else if(command == "rf_jam") {
            // 测试RF干扰功能
            startRFJamming(2440.0, 1);
        } else if(command == "help") {
            printHelp();
        } else {
            Serial.println(F("未知命令，输入help查看帮助"));
        }
    }
}

void printSystemStatus() {
    Serial.println(F("=== 系统状态 ==="));
    Serial.println(F("传感器数据:"));
    Serial.print(F("  温度: "));
    Serial.print(sysData.temperature);
    Serial.println(F("°C"));
    Serial.print(F("  湿度: "));
    Serial.print(sysData.humidity);
    Serial.println(F("%"));
    Serial.print(F("  电池: "));
    Serial.print(sysData.batteryVoltage);
    Serial.print(F("mV ("));
    Serial.print(sysData.batteryPercent);
    Serial.println(F("%)"));

    Serial.println(F("系统状态:"));
    Serial.print(F("  状态: "));
    Serial.println(getStateString());
    Serial.print(F("  健康: "));
    Serial.println(sysData.systemHealthy ? F("正常") : F("异常"));
    Serial.print(F("  运行时间: "));
    Serial.print(sysData.uptime);
    Serial.println(F("秒"));

    Serial.println(F("干扰参数:"));
    Serial.print(F("  频率: "));
    Serial.print(sysData.jamFrequency);
    Serial.println(F("MHz"));
    Serial.print(F("  功率: "));
    Serial.print(sysData.jamPower);
    Serial.println(F("dBm"));
    Serial.print(F("  状态: "));
    Serial.println(sysData.jamEnabled ? F("开启") : F("关闭"));
    Serial.println(F("==============="));
}

void printHelp() {
    Serial.println(F("=== 可用命令 ==="));
    Serial.println(F("页面切换:"));
    Serial.println(F("  main     - 切换到主菜单"));
    Serial.println(F("  recon    - 切换到侦察页面"));
    Serial.println(F("  jamming  - 切换到干扰页面"));
    Serial.println(F("  data     - 切换到数据分析页面"));
    Serial.println(F("  settings - 切换到系统设置页面"));
    Serial.println(F("  error    - 切换到错误显示页面"));
    Serial.println(F("功能控制:"));
    Serial.println(F("  jam_on   - 开启干扰"));
    Serial.println(F("  jam_off  - 关闭干扰"));
    Serial.println(F("系统调试:"));
    Serial.println(F("  status      - 显示系统状态"));
    Serial.println(F("  test_error  - 测试错误显示"));
    Serial.println(F("  clear_error - 清除错误状态"));
    Serial.println(F("按钮测试:"));
    Serial.println(F("  test_button    - 测试导航按钮"));
    Serial.println(F("  test_jam_start - 测试干扰开始按钮"));
    Serial.println(F("RF模块测试:"));
    Serial.println(F("  rf_status - 显示RF模块状态"));
    Serial.println(F("  rf_scan   - 测试RF扫描功能"));
    Serial.println(F("  rf_jam    - 测试RF干扰功能"));
    Serial.println(F("  help      - 显示此帮助"));
    Serial.println(F("==============="));
}
