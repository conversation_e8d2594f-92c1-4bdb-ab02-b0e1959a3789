/*
 * 简化OLED测试程序
 * 专门用于验证OLED硬件连接和基本显示功能
 * 避免复杂的滚动功能，确保兼容性
 */

// 引入统一配置文件
#include "../Master_Controller/Config.h"
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1315.h>

// OLED显示屏配置 (使用Config.h中的定义)
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1
// OLED_I2C_ADDRESS 已在Config.h中定义

// 创建OLED实例
Adafruit_SSD1315 oled(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);

void setup() {
    Serial.begin(115200);
    Serial.println("=== 简化OLED测试程序 ===");
    Serial.println("专门验证OLED基本功能");
    Serial.println();
    
    // 初始化I2C
    Wire.begin();
    Serial.println("I2C初始化完成");
    
    // 初始化OLED显示屏
    Serial.println("正在初始化OLED显示屏...");
    if(!oled.begin(SSD1315_SWITCHCAPVCC, OLED_I2C_ADDRESS)) {
        Serial.println("❌ OLED初始化失败!");
        Serial.println();
        Serial.println("请检查硬件连接:");
        Serial.println("1. VCC → 3.3V (不是5V!)");
        Serial.println("2. GND → GND");
        Serial.println("3. SDA → A4");
        Serial.println("4. SCL → A5");
        Serial.println("5. I2C地址是否为0x3C");
        Serial.println();
        Serial.println("程序将继续尝试...");
        
        // 尝试其他常见I2C地址
        Serial.println("尝试I2C地址0x3D...");
        if(oled.begin(SSD1315_SWITCHCAPVCC, 0x3D)) {
            Serial.println("✅ OLED在地址0x3D初始化成功!");
        } else {
            Serial.println("❌ 所有地址都失败，请检查硬件连接");
            while(1) {
                delay(1000);
                Serial.println("等待硬件连接修复...");
            }
        }
    } else {
        Serial.println("✅ OLED在地址0x3C初始化成功!");
    }
    
    Serial.println("开始显示测试...");
    
    // 显示启动信息
    showStartupInfo();
    delay(3000);
}

void loop() {
    // 测试1: 基本文本显示
    testBasicDisplay();
    delay(3000);
    
    // 测试2: 大字体显示
    testLargeFont();
    delay(3000);
    
    // 测试3: 图形绘制
    testGraphics();
    delay(3000);
    
    // 测试4: 实时数据显示
    testRealTimeData();
    delay(3000);
    
    // 测试5: 系统状态模拟
    testSystemStatus();
    delay(3000);
}

void showStartupInfo() {
    Serial.println("显示启动信息...");
    
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1315_WHITE);
    oled.setCursor(0, 0);
    
    oled.println("OLED Test Success!");
    oled.println("128x64 Display");
    oled.println();
    oled.println("Hardware Check:");
    oled.println("I2C: OK");
    oled.println("Display: OK");
    oled.println();
    oled.println("Ready for testing...");
    
    oled.display();
}

void testBasicDisplay() {
    Serial.println("测试1: 基本文本显示");
    
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1315_WHITE);
    oled.setCursor(0, 0);
    
    oled.println("Basic Text Test");
    oled.println("================");
    oled.println("Line 1: Normal");
    oled.println("Line 2: Numbers 123");
    oled.println("Line 3: Symbols !@#");
    oled.println("Line 4: Chinese 中文");
    oled.println("Line 5: Mixed A1B2");
    oled.println("Test Complete!");
    
    oled.display();
}

void testLargeFont() {
    Serial.println("测试2: 大字体显示");
    
    oled.clearDisplay();
    
    // 大字体标题
    oled.setTextSize(2);
    oled.setTextColor(SSD1315_WHITE);
    oled.setCursor(0, 0);
    oled.println("LARGE");
    oled.println("FONT");
    
    // 小字体说明
    oled.setTextSize(1);
    oled.setCursor(0, 40);
    oled.println("Size 2 font test");
    oled.println("Back to size 1");
    
    oled.display();
}

void testGraphics() {
    Serial.println("测试3: 图形绘制");
    
    oled.clearDisplay();
    
    // 绘制边框
    oled.drawRect(0, 0, OLED_WIDTH-1, OLED_HEIGHT-1, SSD1315_WHITE);
    
    // 绘制内部矩形
    oled.drawRect(10, 10, OLED_WIDTH-21, OLED_HEIGHT-21, SSD1315_WHITE);
    
    // 绘制对角线
    oled.drawLine(0, 0, OLED_WIDTH-1, OLED_HEIGHT-1, SSD1315_WHITE);
    oled.drawLine(OLED_WIDTH-1, 0, 0, OLED_HEIGHT-1, SSD1315_WHITE);
    
    // 绘制圆形
    oled.drawCircle(OLED_WIDTH/2, OLED_HEIGHT/2, 15, SSD1315_WHITE);
    
    // 绘制填充圆形
    oled.fillCircle(OLED_WIDTH/2, OLED_HEIGHT/2, 5, SSD1315_WHITE);
    
    oled.display();
}

void testRealTimeData() {
    Serial.println("测试4: 实时数据显示");
    
    // 模拟实时数据
    float temp = 25.0 + (millis() / 1000) % 10;
    float hum = 50.0 + (millis() / 2000) % 20;
    int batt = 100 - (millis() / 5000) % 30;
    
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1315_WHITE);
    
    // 标题
    oled.setCursor(0, 0);
    oled.println("Real-Time Data");
    oled.drawLine(0, 10, OLED_WIDTH-1, 10, SSD1315_WHITE);
    
    // 温度 (大字体)
    oled.setTextSize(2);
    oled.setCursor(0, 16);
    oled.print(temp, 1);
    oled.println("C");
    
    // 湿度和电池 (小字体)
    oled.setTextSize(1);
    oled.setCursor(0, 40);
    oled.print("Humidity: ");
    oled.print(hum, 0);
    oled.println("%");
    
    oled.setCursor(0, 50);
    oled.print("Battery:  ");
    oled.print(batt);
    oled.println("%");
    
    oled.display();
}

void testSystemStatus() {
    Serial.println("测试5: 系统状态模拟");
    
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1315_WHITE);
    
    // 标题
    oled.setCursor(0, 0);
    oled.println("System Status");
    oled.drawLine(0, 10, OLED_WIDTH-1, 10, SSD1315_WHITE);
    
    // 系统信息
    oled.setCursor(0, 16);
    oled.println("ECM Training System");
    
    oled.setCursor(0, 26);
    oled.print("Uptime: ");
    oled.print(millis() / 1000);
    oled.println("s");
    
    oled.setCursor(0, 36);
    oled.println("Status: NORMAL");
    
    oled.setCursor(0, 46);
    oled.println("OLED:   OK");
    
    oled.setCursor(0, 56);
    oled.println("I2C:    OK");
    
    oled.display();
}
