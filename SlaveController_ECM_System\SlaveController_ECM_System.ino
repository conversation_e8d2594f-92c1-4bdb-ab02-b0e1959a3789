#include <SPI.h>
#include <RF24.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1315.h>
#include <Adafruit_MCP4725.h>

// ==================== 硬件引脚定义 ====================
// 串口通信 (与主控)
#include <SoftwareSerial.h>
#define MASTER_RX_PIN 18       // 接收主控数据的引脚 (MEGA 2560)
#define MASTER_TX_PIN 19       // 发送数据到主控的引脚 (MEGA 2560)
#define MASTER_BAUD_RATE 9600  // SoftwareSerial推荐波特率
SoftwareSerial masterSerial(MASTER_RX_PIN, MASTER_TX_PIN);
#define MASTER_SERIAL masterSerial  // 使用SoftwareSerial与主控通信

// NRF24L01 信号侦察模块
#define RF_CE_PIN 9
#define RF_CSN_PIN 10
// SPI引脚: MOSI=51, MISO=50, SCK=52 (MEGA 2560默认)

// VCO控制 (MCP4725 DAC)
#define DAC_I2C_ADDRESS 0x60

// 功放控制
#define AMPLIFIER_PWM_PIN 6    // PWM控制功放功率
#define AMPLIFIER_ENABLE_PIN 7 // 功放使能控制

// 滤波器控制 (GPIO控制继电器)
#define FILTER_CTRL_PIN1 22    // 滤波器控制位1
#define FILTER_CTRL_PIN2 23    // 滤波器控制位2
#define FILTER_CTRL_PIN3 24    // 滤波器控制位3

// OLED显示屏 (SSD1315, 0.96寸黄蓝双色)
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1
// I2C引脚: SDA=20, SCL=21 (MEGA 2560默认)

// 状态LED
#define STATUS_LED_READY 25    // 就绪状态LED (绿色)
#define STATUS_LED_JAMMING 26  // 干扰状态LED (红色)
#define STATUS_LED_SCANNING 27 // 侦察状态LED (蓝色)

// ==================== 系统配置 ====================
#define SYSTEM_VERSION "1.0.0"
#define SLAVE_ID 0x01
#define DEBUG_MODE 1

// ==================== 通信协议 ====================
#define COMM_START_BYTE 0xAA
#define COMM_END_BYTE 0x55
#define COMM_TIMEOUT_MS 1000
#define COMM_RETRY_COUNT 3

// ==================== VCO配置 ====================
#define VCO_MIN_FREQ_MHZ 88.0
#define VCO_MAX_FREQ_MHZ 108.0
#define VCO_STEP_SIZE_KHZ 100.0
#define VCO_DAC_MIN 0
#define VCO_DAC_MAX 4095

// ==================== 滤波器配置 ====================
#define FILTER_BAND_COUNT 8
#define FILTER_DEFAULT_BAND 0

// ==================== 功放配置 ====================
#define AMPLIFIER_MIN_POWER 0
#define AMPLIFIER_MAX_POWER 255
#define AMPLIFIER_DEFAULT_POWER 128

// ==================== 全局实例 ====================
RF24 radio(RF_CE_PIN, RF_CSN_PIN);
Adafruit_SSD1315 display(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);
Adafruit_MCP4725 dac;

// ==================== 数据结构定义 ====================
// 指令类型
enum CommandType {
  CMD_SET_FREQUENCY = 0x01,
  CMD_SET_POWER = 0x02,
  CMD_SET_FILTER = 0x03,
  CMD_START_JAMMING = 0x04,
  CMD_STOP_JAMMING = 0x05,
  CMD_START_SCANNING = 0x06,
  CMD_STOP_SCANNING = 0x07,
  CMD_GET_STATUS = 0x08,
  CMD_CALIBRATE_VCO = 0x09
};

// 状态反馈类型
enum StatusType {
  STATUS_READY = 0x01,
  STATUS_JAMMING = 0x02,
  STATUS_SCANNING = 0x03,
  STATUS_ERROR = 0x04,
  STATUS_CALIBRATING = 0x05
};

// 通信数据包结构
struct CommPacket {
  uint8_t startByte;
  uint8_t command;
  uint16_t dataLength;
  uint8_t data[32];
  uint8_t checksum;
  uint8_t endByte;
};

// VCO校准数据点
struct CalibrationPoint {
  float frequency;  // MHz
  uint16_t dacValue;
  bool isValid;
};

// VCO校准表
struct VCOCalibrationTable {
  CalibrationPoint points[21];  // 88-108MHz, 每1MHz一个点
  uint8_t validPointCount;
  bool isCalibrated;
};

// ==================== 系统状态 ====================
struct SystemStatus {
  StatusType currentStatus;
  float currentFrequency;
  uint8_t currentPower;
  uint8_t currentFilter;
  bool amplifierEnabled;
  uint32_t lastCommandTime;
  uint16_t errorCode;
};

// 全局变量
SystemStatus systemStatus;
VCOCalibrationTable vcoCalTable;
CommPacket rxPacket, txPacket;
bool newCommandReceived = false;

void setup() {
  // 初始化串口
  Serial.begin(115200);
  MASTER_SERIAL.begin(MASTER_BAUD_RATE);
  
  // 初始化状态LED
  pinMode(STATUS_LED_READY, OUTPUT);
  pinMode(STATUS_LED_JAMMING, OUTPUT);
  pinMode(STATUS_LED_SCANNING, OUTPUT);
  
  // 初始化功放控制
  pinMode(AMPLIFIER_PWM_PIN, OUTPUT);
  pinMode(AMPLIFIER_ENABLE_PIN, OUTPUT);
  digitalWrite(AMPLIFIER_ENABLE_PIN, LOW);
  
  // 初始化滤波器控制
  pinMode(FILTER_CTRL_PIN1, OUTPUT);
  pinMode(FILTER_CTRL_PIN2, OUTPUT);
  pinMode(FILTER_CTRL_PIN3, OUTPUT);
  
  // 初始化I2C
  Wire.begin();
  
  // 初始化DAC
  if (!dac.begin(DAC_I2C_ADDRESS)) {
    Serial.println("DAC初始化失败!");
    systemStatus.errorCode = 0x01;
  }
  
  // 初始化OLED
  if (!display.begin(SSD1315_SWITCHCAPVCC, 0x3C)) {
    Serial.println("OLED初始化失败!");
    systemStatus.errorCode = 0x02;
  }
  
  // 初始化NRF24L01
  if (!radio.begin()) {
    Serial.println("NRF24L01初始化失败!");
    systemStatus.errorCode = 0x03;
  }
  
  // 初始化系统状态
  systemStatus.currentStatus = STATUS_READY;
  systemStatus.currentFrequency = 100.0;
  systemStatus.currentPower = AMPLIFIER_DEFAULT_POWER;
  systemStatus.currentFilter = FILTER_DEFAULT_BAND;
  systemStatus.amplifierEnabled = false;
  systemStatus.lastCommandTime = millis();
  systemStatus.errorCode = 0x00;
  
  // 初始化VCO校准表
  initVCOCalibrationTable();
  
  // 显示启动信息
  displayStartupInfo();
  
  // 设置就绪状态
  setStatusLED(STATUS_READY);
  
  Serial.println("副控系统初始化完成");
}

void loop() {
  // 检查主控通信
  checkMasterCommunication();
  
  // 处理接收到的指令
  if (newCommandReceived) {
    processCommand();
    newCommandReceived = false;
  }
  
  // 更新显示
  updateDisplay();
  
  // 检查系统状态
  checkSystemHealth();

  // 执行信号侦察 (如果处于侦察状态)
  if (systemStatus.currentStatus == STATUS_SCANNING) {
    performSignalDetection();
  }

  delay(10);
}

void checkMasterCommunication() {
  if (MASTER_SERIAL.available()) {
    if (receivePacket()) {
      newCommandReceived = true;
    }
  }
}

bool receivePacket() {
  // 简化的数据包接收逻辑
  if (MASTER_SERIAL.read() == COMM_START_BYTE) {
    rxPacket.startByte = COMM_START_BYTE;
    rxPacket.command = MASTER_SERIAL.read();
    // 继续接收数据包...
    return true;
  }
  return false;
}

void processCommand() {
  switch (rxPacket.command) {
    case CMD_SET_FREQUENCY:
      setFrequency(*((float*)rxPacket.data));
      break;
    case CMD_SET_POWER:
      setPower(rxPacket.data[0]);
      break;
    case CMD_SET_FILTER:
      setFilter(rxPacket.data[0]);
      break;
    case CMD_START_JAMMING:
      startJamming();
      break;
    case CMD_STOP_JAMMING:
      stopJamming();
      break;
    case CMD_START_SCANNING:
      startScanning();
      break;
    case CMD_STOP_SCANNING:
      stopScanning();
      break;
    case CMD_GET_STATUS:
      sendStatus();
      break;
    case CMD_CALIBRATE_VCO:
      calibrateVCO();
      break;
  }
}

void setFrequency(float frequency) {
  if (frequency >= VCO_MIN_FREQ_MHZ && frequency <= VCO_MAX_FREQ_MHZ) {
    systemStatus.currentFrequency = frequency;
    uint16_t dacValue = frequencyToDAC(frequency);
    dac.setVoltage(dacValue, false);
    Serial.print("频率设置为: ");
    Serial.print(frequency);
    Serial.println(" MHz");
  }
}

void setPower(uint8_t power) {
  if (power <= AMPLIFIER_MAX_POWER) {
    systemStatus.currentPower = power;
    analogWrite(AMPLIFIER_PWM_PIN, power);
    Serial.print("功率设置为: ");
    Serial.println(power);
  }
}

void setFilter(uint8_t filterBand) {
  if (filterBand < FILTER_BAND_COUNT) {
    systemStatus.currentFilter = filterBand;
    // 设置滤波器继电器
    digitalWrite(FILTER_CTRL_PIN1, filterBand & 0x01);
    digitalWrite(FILTER_CTRL_PIN2, (filterBand >> 1) & 0x01);
    digitalWrite(FILTER_CTRL_PIN3, (filterBand >> 2) & 0x01);
    Serial.print("滤波器设置为: ");
    Serial.println(filterBand);
  }
}

void startJamming() {
  systemStatus.currentStatus = STATUS_JAMMING;
  systemStatus.amplifierEnabled = true;
  digitalWrite(AMPLIFIER_ENABLE_PIN, HIGH);
  setStatusLED(STATUS_JAMMING);
  Serial.println("开始干扰");
}

void stopJamming() {
  systemStatus.currentStatus = STATUS_READY;
  systemStatus.amplifierEnabled = false;
  digitalWrite(AMPLIFIER_ENABLE_PIN, LOW);
  setStatusLED(STATUS_READY);
  Serial.println("停止干扰");
}

void startScanning() {
  systemStatus.currentStatus = STATUS_SCANNING;
  setStatusLED(STATUS_SCANNING);

  // 配置NRF24L01监听主控通信
  if (radio.begin()) {
    radio.setPALevel(RF24_PA_LOW);
    radio.setDataRate(RF24_250KBPS);
    radio.setChannel(40); // 监听主控NRF24L01通信频道 (2440MHz)
    radio.setAutoAck(false);

    // 设置接收地址与主控一致
    uint8_t address[6] = "ENEMY";
    radio.openReadingPipe(1, address);
    radio.startListening();

    Serial.println("开始侦察主控通信 - 频道40 (2440MHz)");
  } else {
    Serial.println("NRF24L01初始化失败");
  }
}

void stopScanning() {
  systemStatus.currentStatus = STATUS_READY;
  setStatusLED(STATUS_READY);

  // 停止NRF24L01监听
  radio.stopListening();

  Serial.println("停止侦察");
}

// 执行信号检测 (双RF24通信对抗核心功能)
void performSignalDetection() {
  static unsigned long lastDetectionTime = 0;
  static int detectedPackets = 0;

  // 每500ms检查一次
  if (millis() - lastDetectionTime < 500) return;
  lastDetectionTime = millis();

  // 检查是否有数据包可用
  if (radio.available()) {
    uint8_t buffer[32];
    uint8_t len = radio.getDynamicPayloadSize();

    if (len > 0 && len <= 32) {
      radio.read(buffer, len);
      detectedPackets++;

      Serial.print("检测到主控通信包: 长度=");
      Serial.print(len);
      Serial.print(", 总计=");
      Serial.println(detectedPackets);

      // 如果检测到足够的通信包，启动干扰
      if (detectedPackets >= 3 && systemStatus.currentStatus == STATUS_SCANNING) {
        Serial.println("检测到活跃通信，自动启动干扰");
        startJamming();
        detectedPackets = 0; // 重置计数
      }
    }
  } else {
    // 没有检测到信号，如果正在干扰则考虑停止
    static int noSignalCount = 0;
    noSignalCount++;

    if (noSignalCount >= 6 && systemStatus.currentStatus == STATUS_JAMMING) {
      // 连续3秒没有检测到信号，停止干扰
      Serial.println("目标信号消失，停止干扰");
      stopJamming();
      noSignalCount = 0;

      // 重新开始侦察
      delay(1000);
      startScanning();
    }
  }
}

void sendStatus() {
  // 发送状态数据包到主控
  txPacket.startByte = COMM_START_BYTE;
  txPacket.command = CMD_GET_STATUS;
  // 填充状态数据...
  txPacket.endByte = COMM_END_BYTE;
  
  MASTER_SERIAL.write((uint8_t*)&txPacket, sizeof(txPacket));
}

void calibrateVCO() {
  systemStatus.currentStatus = STATUS_CALIBRATING;
  setStatusLED(STATUS_CALIBRATING);
  
  // VCO校准逻辑
  for (int i = 0; i < 21; i++) {
    float freq = 88.0 + i * 1.0;
    uint16_t dacValue = performFrequencyMeasurement(freq);
    
    vcoCalTable.points[i].frequency = freq;
    vcoCalTable.points[i].dacValue = dacValue;
    vcoCalTable.points[i].isValid = true;
    
    delay(100);
  }
  
  vcoCalTable.isCalibrated = true;
  vcoCalTable.validPointCount = 21;
  
  systemStatus.currentStatus = STATUS_READY;
  setStatusLED(STATUS_READY);
  Serial.println("VCO校准完成");
}

uint16_t performFrequencyMeasurement(float targetFreq) {
  // 简化的频率测量逻辑
  // 实际应该使用频率计数器或其他测量方法
  return map(targetFreq * 100, VCO_MIN_FREQ_MHZ * 100, VCO_MAX_FREQ_MHZ * 100, VCO_DAC_MIN, VCO_DAC_MAX);
}

uint16_t frequencyToDAC(float frequency) {
  if (!vcoCalTable.isCalibrated) {
    // 使用线性插值作为默认值
    return map(frequency * 100, VCO_MIN_FREQ_MHZ * 100, VCO_MAX_FREQ_MHZ * 100, VCO_DAC_MIN, VCO_DAC_MAX);
  }
  
  // 使用校准表进行插值
  for (int i = 0; i < vcoCalTable.validPointCount - 1; i++) {
    if (frequency >= vcoCalTable.points[i].frequency && 
        frequency <= vcoCalTable.points[i + 1].frequency) {
      // 线性插值
      float ratio = (frequency - vcoCalTable.points[i].frequency) / 
                   (vcoCalTable.points[i + 1].frequency - vcoCalTable.points[i].frequency);
      return vcoCalTable.points[i].dacValue + 
             ratio * (vcoCalTable.points[i + 1].dacValue - vcoCalTable.points[i].dacValue);
    }
  }
  
  return VCO_DAC_MIN;
}

void setStatusLED(StatusType status) {
  // 关闭所有LED
  digitalWrite(STATUS_LED_READY, LOW);
  digitalWrite(STATUS_LED_JAMMING, LOW);
  digitalWrite(STATUS_LED_SCANNING, LOW);
  
  // 点亮对应状态LED
  switch (status) {
    case STATUS_READY:
      digitalWrite(STATUS_LED_READY, HIGH);
      break;
    case STATUS_JAMMING:
      digitalWrite(STATUS_LED_JAMMING, HIGH);
      break;
    case STATUS_SCANNING:
      digitalWrite(STATUS_LED_SCANNING, HIGH);
      break;
    case STATUS_CALIBRATING:
      // 校准时闪烁所有LED
      digitalWrite(STATUS_LED_READY, HIGH);
      digitalWrite(STATUS_LED_JAMMING, HIGH);
      digitalWrite(STATUS_LED_SCANNING, HIGH);
      break;
  }
}

void initVCOCalibrationTable() {
  vcoCalTable.isCalibrated = false;
  vcoCalTable.validPointCount = 0;
  
  for (int i = 0; i < 21; i++) {
    vcoCalTable.points[i].frequency = 0.0;
    vcoCalTable.points[i].dacValue = 0;
    vcoCalTable.points[i].isValid = false;
  }
}

void displayStartupInfo() {
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1315_WHITE);
  display.setCursor(0, 0);
  display.println("ECM Slave Controller");
  display.print("Version: ");
  display.println(SYSTEM_VERSION);
  display.print("ID: 0x");
  display.println(SLAVE_ID, HEX);
  display.println("Initializing...");
  display.display();
  delay(2000);
}

void updateDisplay() {
  static unsigned long lastUpdate = 0;
  if (millis() - lastUpdate > 500) {  // 每500ms更新一次
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1315_WHITE);
    display.setCursor(0, 0);
    
    // 显示状态
    display.print("Status: ");
    switch (systemStatus.currentStatus) {
      case STATUS_READY:
        display.println("READY");
        break;
      case STATUS_JAMMING:
        display.println("JAMMING");
        break;
      case STATUS_SCANNING:
        display.println("SCANNING");
        break;
      case STATUS_CALIBRATING:
        display.println("CALIBRATING");
        break;
      case STATUS_ERROR:
        display.println("ERROR");
        break;
    }
    
    // 显示频率
    display.print("Freq: ");
    display.print(systemStatus.currentFrequency, 1);
    display.println(" MHz");
    
    // 显示功率
    display.print("Power: ");
    display.print(systemStatus.currentPower);
    display.println("/255");
    
    // 显示滤波器
    display.print("Filter: ");
    display.println(systemStatus.currentFilter);
    
    // 显示功放状态
    display.print("Amp: ");
    display.println(systemStatus.amplifierEnabled ? "ON" : "OFF");
    
    display.display();
    lastUpdate = millis();
  }
}

void checkSystemHealth() {
  // 检查通信超时
  if (millis() - systemStatus.lastCommandTime > COMM_TIMEOUT_MS * 10) {
    // 长时间无通信，进入安全模式
    if (systemStatus.currentStatus == STATUS_JAMMING) {
      stopJamming();
      Serial.println("通信超时，自动停止干扰");
    }
  }
  
  // 检查错误状态
  if (systemStatus.errorCode != 0x00) {
    systemStatus.currentStatus = STATUS_ERROR;
    setStatusLED(STATUS_ERROR);
  }
}