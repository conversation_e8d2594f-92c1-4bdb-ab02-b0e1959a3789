#include <SPI.h>
#include <RF24.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <Adafruit_MCP4725.h>

// ==================== 硬件引脚定义 ====================
// 串口通信 (与主控) - 使用硬件串口1
#define MASTER_SERIAL Serial1  // 硬件串口1与主控通信
#define MASTER_BAUD_RATE 115200 // 通信波特率

// NRF24L01 信号侦察模块
#define RF_CE_PIN 9
#define RF_CSN_PIN 10
// SPI引脚: MOSI=51, MISO=50, SCK=52 (MEGA 2560默认)

// VCO控制 (MCP4725 DAC)
#define DAC_I2C_ADDRESS 0x60

// 功放控制
#define AMPLIFIER_PWM_PIN 6    // PWM控制功放功率
#define AMPLIFIER_ENABLE_PIN 7 // 功放使能控制

// 滤波器控制 (GPIO控制继电器)
#define FILTER_CTRL_PIN1 22    // 滤波器控制位1
#define FILTER_CTRL_PIN2 23    // 滤波器控制位2
#define FILTER_CTRL_PIN3 24    // 滤波器控制位3

// OLED显示屏 (SSD1315, 0.96寸黄蓝双色)
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1
// I2C引脚: SDA=20, SCL=21 (MEGA 2560默认)

// 状态LED
#define STATUS_LED_READY 25    // 就绪状态LED (绿色)
#define STATUS_LED_JAMMING 26  // 干扰状态LED (红色)
#define STATUS_LED_SCANNING 27 // 侦察状态LED (蓝色)

// ==================== 系统配置 ====================
#define SYSTEM_VERSION "1.0.0"
#define SLAVE_ID 0x01
#define DEBUG_MODE 1

// ==================== 通信协议 ====================
#define COMM_START_BYTE 0xAA
#define COMM_END_BYTE 0x55
#define COMM_TIMEOUT_MS 1000
#define COMM_RETRY_COUNT 3

// ==================== VCO配置 (基于KVCO-2400实测数据) ====================
#define VCO_MIN_FREQ_MHZ 2399.26  // 最小频率 (对应0.7V)
#define VCO_MAX_FREQ_MHZ 2460.46  // 最大频率 (对应1.5V)
#define VCO_MIN_VOLTAGE 0.7       // 最小控制电压
#define VCO_MAX_VOLTAGE 1.5       // 最大控制电压
#define VCO_DAC_MIN 0
#define VCO_DAC_MAX 4095

// ==================== 滤波器配置 ====================
#define FILTER_BAND_COUNT 8
#define FILTER_DEFAULT_BAND 0

// ==================== 功放配置 ====================
#define AMPLIFIER_MIN_POWER 0
#define AMPLIFIER_MAX_POWER 255
#define AMPLIFIER_DEFAULT_POWER 128

// ==================== 全局实例 ====================
RF24 radio(RF_CE_PIN, RF_CSN_PIN);
Adafruit_SSD1306 display(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);
Adafruit_MCP4725 dac;

// ==================== 数据结构定义 ====================
// 指令类型
enum CommandType {
  CMD_SET_FREQUENCY = 0x01,
  CMD_SET_POWER = 0x02,
  CMD_SET_FILTER = 0x03,
  CMD_START_JAMMING = 0x04,
  CMD_STOP_JAMMING = 0x05,
  CMD_START_SCANNING = 0x06,
  CMD_STOP_SCANNING = 0x07,
  CMD_GET_STATUS = 0x08,
  CMD_CALIBRATE_VCO = 0x09
};

// 状态反馈类型
enum StatusType {
  STATUS_READY = 0x01,
  STATUS_JAMMING = 0x02,
  STATUS_SCANNING = 0x03,
  STATUS_ERROR = 0x04,
  STATUS_CALIBRATING = 0x05
};

// 通信数据包结构
struct CommPacket {
  uint8_t startByte;
  uint8_t command;
  uint16_t dataLength;
  uint8_t data[32];
  uint8_t checksum;
  uint8_t endByte;
};

// VCO校准数据点 (基于KVCO-2400实测数据)
struct VCOCalibrationPoint {
  float voltage;    // 控制电压 (V)
  float frequency;  // 输出频率 (MHz)
  float amplitude;  // 输出幅值 (dBm)
};

// VCO校准数据表 (系统可用范围: 2399-2460MHz)
const VCOCalibrationPoint vcoCalibrationTable[] = {
  {0.7, 2399.26, 7.99},  // 接近滤波器下限
  {0.8, 2406.66, 7.97},
  {0.9, 2414.11, 7.95},
  {1.0, 2421.66, 7.92},
  {1.1, 2429.21, 7.89},  // 接近滤波器中心
  {1.2, 2436.86, 7.82},
  {1.3, 2444.61, 7.85},
  {1.4, 2452.46, 7.85},
  {1.5, 2460.46, 7.86}   // 接近滤波器上限
};

const int VCO_CALIBRATION_POINTS = sizeof(vcoCalibrationTable) / sizeof(VCOCalibrationPoint);

// ==================== 系统状态 ====================
struct SystemStatus {
  StatusType currentStatus;
  float currentFrequency;
  uint8_t currentPower;
  uint8_t currentFilter;
  bool amplifierEnabled;
  uint32_t lastCommandTime;
  uint16_t errorCode;
};

// 全局变量
SystemStatus systemStatus;
CommPacket rxPacket, txPacket;
bool newCommandReceived = false;

void setup() {
  // 初始化串口
  Serial.begin(115200);
  MASTER_SERIAL.begin(MASTER_BAUD_RATE);
  
  // 初始化状态LED
  pinMode(STATUS_LED_READY, OUTPUT);
  pinMode(STATUS_LED_JAMMING, OUTPUT);
  pinMode(STATUS_LED_SCANNING, OUTPUT);
  
  // 初始化功放控制
  pinMode(AMPLIFIER_PWM_PIN, OUTPUT);
  pinMode(AMPLIFIER_ENABLE_PIN, OUTPUT);
  digitalWrite(AMPLIFIER_ENABLE_PIN, LOW);
  
  // 初始化滤波器控制
  pinMode(FILTER_CTRL_PIN1, OUTPUT);
  pinMode(FILTER_CTRL_PIN2, OUTPUT);
  pinMode(FILTER_CTRL_PIN3, OUTPUT);
  
  // 初始化I2C
  Wire.begin();
  
  // 初始化DAC
  if (!dac.begin(DAC_I2C_ADDRESS)) {
    Serial.println("DAC初始化失败!");
    systemStatus.errorCode = 0x01;
  }
  
  // 初始化OLED
  if (!display.begin(SSD1315_SWITCHCAPVCC, 0x3C)) {
    Serial.println("OLED初始化失败!");
    systemStatus.errorCode = 0x02;
  }
  
  // 初始化NRF24L01
  if (!radio.begin()) {
    Serial.println("NRF24L01初始化失败!");
    systemStatus.errorCode = 0x03;
  }
  
  // 初始化系统状态
  systemStatus.currentStatus = STATUS_READY;
  systemStatus.currentFrequency = 100.0;
  systemStatus.currentPower = AMPLIFIER_DEFAULT_POWER;
  systemStatus.currentFilter = FILTER_DEFAULT_BAND;
  systemStatus.amplifierEnabled = false;
  systemStatus.lastCommandTime = millis();
  systemStatus.errorCode = 0x00;
  
  // VCO校准表已内置实测数据，无需初始化
  
  // 显示启动信息
  displayStartupInfo();
  
  // 设置就绪状态
  setStatusLED(STATUS_READY);
  
  Serial.println("副控系统初始化完成");
}

void loop() {
  // 检查主控通信
  checkMasterCommunication();
  
  // 处理接收到的指令
  if (newCommandReceived) {
    processCommand();
    newCommandReceived = false;
  }
  
  // 更新显示
  updateDisplay();
  
  // 检查系统状态
  checkSystemHealth();

  // 执行信号侦察 (如果处于侦察状态)
  if (systemStatus.currentStatus == STATUS_SCANNING) {
    performSignalDetection();
  }

  delay(10);
}

void checkMasterCommunication() {
  if (MASTER_SERIAL.available()) {
    if (receivePacket()) {
      newCommandReceived = true;
    }
  }
}

bool receivePacket() {
  // 简化的数据包接收逻辑
  if (MASTER_SERIAL.read() == COMM_START_BYTE) {
    rxPacket.startByte = COMM_START_BYTE;
    rxPacket.command = MASTER_SERIAL.read();
    // 继续接收数据包...
    return true;
  }
  return false;
}

void processCommand() {
  switch (rxPacket.command) {
    case CMD_SET_FREQUENCY:
      setFrequency(*((float*)rxPacket.data));
      break;
    case CMD_SET_POWER:
      setPower(rxPacket.data[0]);
      break;
    case CMD_SET_FILTER:
      setFilter(rxPacket.data[0]);
      break;
    case CMD_START_JAMMING:
      startJamming();
      break;
    case CMD_STOP_JAMMING:
      stopJamming();
      break;
    case CMD_START_SCANNING:
      startScanning();
      break;
    case CMD_STOP_SCANNING:
      stopScanning();
      break;
    case CMD_GET_STATUS:
      sendStatus();
      break;
    case CMD_CALIBRATE_VCO:
      // VCO校准功能已移除 - 使用内置实测数据
      Serial.println("VCO使用内置实测数据，无需校准");
      break;
  }
}

void setFrequency(float frequency) {
  if (frequency >= VCO_MIN_FREQ_MHZ && frequency <= VCO_MAX_FREQ_MHZ) {
    systemStatus.currentFrequency = frequency;
    uint16_t dacValue = frequencyToDAC(frequency);
    dac.setVoltage(dacValue, false);
    Serial.print("频率设置为: ");
    Serial.print(frequency);
    Serial.println(" MHz");
  }
}

void setPower(uint8_t power) {
  if (power <= AMPLIFIER_MAX_POWER) {
    systemStatus.currentPower = power;
    analogWrite(AMPLIFIER_PWM_PIN, power);
    Serial.print("功率设置为: ");
    Serial.println(power);
  }
}

void setFilter(uint8_t filterBand) {
  if (filterBand < FILTER_BAND_COUNT) {
    systemStatus.currentFilter = filterBand;
    // 设置滤波器继电器
    digitalWrite(FILTER_CTRL_PIN1, filterBand & 0x01);
    digitalWrite(FILTER_CTRL_PIN2, (filterBand >> 1) & 0x01);
    digitalWrite(FILTER_CTRL_PIN3, (filterBand >> 2) & 0x01);
    Serial.print("滤波器设置为: ");
    Serial.println(filterBand);
  }
}

void startJamming() {
  systemStatus.currentStatus = STATUS_JAMMING;
  systemStatus.amplifierEnabled = true;
  digitalWrite(AMPLIFIER_ENABLE_PIN, HIGH);
  setStatusLED(STATUS_JAMMING);
  Serial.println("开始干扰");
}

void stopJamming() {
  systemStatus.currentStatus = STATUS_READY;
  systemStatus.amplifierEnabled = false;
  digitalWrite(AMPLIFIER_ENABLE_PIN, LOW);
  setStatusLED(STATUS_READY);
  Serial.println("停止干扰");
}

void startScanning() {
  systemStatus.currentStatus = STATUS_SCANNING;
  setStatusLED(STATUS_SCANNING);

  // 配置NRF24L01监听主控通信
  if (radio.begin()) {
    radio.setPALevel(RF24_PA_LOW);
    radio.setDataRate(RF24_250KBPS);
    radio.setChannel(40); // 监听主控NRF24L01通信频道 (2440MHz)
    radio.setAutoAck(false);
    radio.setDataRate(RF24_250KBPS);  // 与主控一致

    // 设置接收地址与主控一致
    uint8_t address[6] = "ENEMY";
    radio.openReadingPipe(1, address);
    radio.startListening();

    Serial.println("开始侦察主控通信 - 频道40 (2440MHz)");
  } else {
    Serial.println("NRF24L01初始化失败");
  }
}

void stopScanning() {
  systemStatus.currentStatus = STATUS_READY;
  setStatusLED(STATUS_READY);

  // 停止NRF24L01监听
  radio.stopListening();

  Serial.println("停止侦察");
}

// 执行信号检测 (双RF24通信对抗核心功能)
void performSignalDetection() {
  static unsigned long lastDetectionTime = 0;
  static int detectedPackets = 0;

  // 每500ms检查一次
  if (millis() - lastDetectionTime < 500) return;
  lastDetectionTime = millis();

  // 检查是否有数据包可用
  if (radio.available()) {
    uint8_t buffer[32];
    uint8_t len = radio.getDynamicPayloadSize();

    if (len > 0 && len <= 32) {
      radio.read(buffer, len);
      detectedPackets++;

      Serial.print("检测到主控通信包: 长度=");
      Serial.print(len);
      Serial.print(", 总计=");
      Serial.println(detectedPackets);

      // 如果检测到足够的通信包，启动干扰
      if (detectedPackets >= 3 && systemStatus.currentStatus == STATUS_SCANNING) {
        Serial.println("检测到活跃通信，自动启动干扰");
        startJamming();
        detectedPackets = 0; // 重置计数
      }
    }
  } else {
    // 没有检测到信号，如果正在干扰则考虑停止
    static int noSignalCount = 0;
    noSignalCount++;

    if (noSignalCount >= 6 && systemStatus.currentStatus == STATUS_JAMMING) {
      // 连续3秒没有检测到信号，停止干扰
      Serial.println("目标信号消失，停止干扰");
      stopJamming();
      noSignalCount = 0;

      // 重新开始侦察
      delay(1000);
      startScanning();
    }
  }
}

void sendStatus() {
  // 发送状态数据包到主控
  txPacket.startByte = COMM_START_BYTE;
  txPacket.command = CMD_GET_STATUS;
  // 填充状态数据...
  txPacket.endByte = COMM_END_BYTE;
  
  MASTER_SERIAL.write((uint8_t*)&txPacket, sizeof(txPacket));
}

// ==================== VCO频率控制 (基于实测数据) ====================
float frequencyToVoltage(float frequency) {
  // 限制频率范围
  if (frequency < VCO_MIN_FREQ_MHZ) frequency = VCO_MIN_FREQ_MHZ;
  if (frequency > VCO_MAX_FREQ_MHZ) frequency = VCO_MAX_FREQ_MHZ;

  // 在校准表中查找最接近的点进行线性插值
  for (int i = 0; i < VCO_CALIBRATION_POINTS - 1; i++) {
    if (frequency >= vcoCalibrationTable[i].frequency &&
        frequency <= vcoCalibrationTable[i + 1].frequency) {

      // 线性插值计算电压
      float freq1 = vcoCalibrationTable[i].frequency;
      float freq2 = vcoCalibrationTable[i + 1].frequency;
      float volt1 = vcoCalibrationTable[i].voltage;
      float volt2 = vcoCalibrationTable[i + 1].voltage;

      float voltage = volt1 + (frequency - freq1) * (volt2 - volt1) / (freq2 - freq1);

      Serial.print("频率 ");
      Serial.print(frequency, 2);
      Serial.print("MHz → 电压 ");
      Serial.print(voltage, 3);
      Serial.println("V");

      return voltage;
    }
  }

  // 如果超出范围，使用边界值
  if (frequency <= vcoCalibrationTable[0].frequency) {
    return vcoCalibrationTable[0].voltage;
  } else {
    return vcoCalibrationTable[VCO_CALIBRATION_POINTS - 1].voltage;
  }
}

uint16_t frequencyToDAC(float frequency) {
  // 计算控制电压
  float voltage = frequencyToVoltage(frequency);

  // 转换为DAC数值 (12位，0-4095)
  // DAC输出范围: 0-5V，所以 voltage/5.0 * 4095
  uint16_t dacValue = (uint16_t)(voltage / 5.0 * 4095);

  Serial.print("VCO频率设置: ");
  Serial.print(frequency, 2);
  Serial.print("MHz (");
  Serial.print(voltage, 3);
  Serial.print("V, DAC=");
  Serial.print(dacValue);
  Serial.println(")");

  return dacValue;
}

void setStatusLED(StatusType status) {
  // 关闭所有LED
  digitalWrite(STATUS_LED_READY, LOW);
  digitalWrite(STATUS_LED_JAMMING, LOW);
  digitalWrite(STATUS_LED_SCANNING, LOW);
  
  // 点亮对应状态LED
  switch (status) {
    case STATUS_READY:
      digitalWrite(STATUS_LED_READY, HIGH);
      break;
    case STATUS_JAMMING:
      digitalWrite(STATUS_LED_JAMMING, HIGH);
      break;
    case STATUS_SCANNING:
      digitalWrite(STATUS_LED_SCANNING, HIGH);
      break;
    case STATUS_CALIBRATING:
      // 校准时闪烁所有LED
      digitalWrite(STATUS_LED_READY, HIGH);
      digitalWrite(STATUS_LED_JAMMING, HIGH);
      digitalWrite(STATUS_LED_SCANNING, HIGH);
      break;
  }
}

void initVCOCalibrationTable() {
  vcoCalTable.isCalibrated = false;
  vcoCalTable.validPointCount = 0;
  
  for (int i = 0; i < 21; i++) {
    vcoCalTable.points[i].frequency = 0.0;
    vcoCalTable.points[i].dacValue = 0;
    vcoCalTable.points[i].isValid = false;
  }
}

void displayStartupInfo() {
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1315_WHITE);
  display.setCursor(0, 0);
  display.println("ECM Slave Controller");
  display.print("Version: ");
  display.println(SYSTEM_VERSION);
  display.print("ID: 0x");
  display.println(SLAVE_ID, HEX);
  display.println("Initializing...");
  display.display();
  delay(2000);
}

void updateDisplay() {
  static unsigned long lastUpdate = 0;
  if (millis() - lastUpdate > 500) {  // 每500ms更新一次
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1315_WHITE);
    display.setCursor(0, 0);
    
    // 显示状态
    display.print("Status: ");
    switch (systemStatus.currentStatus) {
      case STATUS_READY:
        display.println("READY");
        break;
      case STATUS_JAMMING:
        display.println("JAMMING");
        break;
      case STATUS_SCANNING:
        display.println("SCANNING");
        break;
      case STATUS_CALIBRATING:
        display.println("CALIBRATING");
        break;
      case STATUS_ERROR:
        display.println("ERROR");
        break;
    }
    
    // 显示频率
    display.print("Freq: ");
    display.print(systemStatus.currentFrequency, 1);
    display.println(" MHz");
    
    // 显示功率
    display.print("Power: ");
    display.print(systemStatus.currentPower);
    display.println("/255");
    
    // 显示滤波器
    display.print("Filter: ");
    display.println(systemStatus.currentFilter);
    
    // 显示功放状态
    display.print("Amp: ");
    display.println(systemStatus.amplifierEnabled ? "ON" : "OFF");
    
    display.display();
    lastUpdate = millis();
  }
}

void checkSystemHealth() {
  // 检查通信超时
  if (millis() - systemStatus.lastCommandTime > COMM_TIMEOUT_MS * 10) {
    // 长时间无通信，进入安全模式
    if (systemStatus.currentStatus == STATUS_JAMMING) {
      stopJamming();
      Serial.println("通信超时，自动停止干扰");
    }
  }
  
  // 检查错误状态
  if (systemStatus.errorCode != 0x00) {
    systemStatus.currentStatus = STATUS_ERROR;
    setStatusLED(STATUS_ERROR);
  }
}