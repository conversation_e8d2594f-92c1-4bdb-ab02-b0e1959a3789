# 通信协议设计

## 1. 系统通信架构

### 1.1 通信层次结构
```
应用层：训练指令、状态反馈、数据传输
传输层：数据包封装、错误检测、重传机制
物理层：NRF24L01无线通信、串口通信、I2C/SPI通信
```

### 1.2 通信拓扑
```
主控 (UNO R4 WiFi) ←→ NRF24L01 ←→ NRF24L01 ←→ 副控 (MEGA 2560)
    ↓                                                    ↓
TJC串口屏                                           OLED + 射频模块
    ↓                                                    ↓
WiFi远程控制                                        MCP4725 + VCO + 滤波器 + 功放
```

## 2. 主副控通信协议

### 2.1 数据包格式
```
数据包结构 (32字节)：
+--------+--------+--------+--------+--------+--------+--------+--------+
| Header | SeqNum | CmdID  | Length | Data (24 bytes)              | CRC  |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   1B   |   1B   |   1B   |   1B   |         24B                  |  4B  |
```

**字段说明：**
- Header (1B): 包头标识 (0xAA)
- SeqNum (1B): 序列号 (0-255循环)
- CmdID (1B): 命令ID
- Length (1B): 数据长度 (0-24)
- Data (24B): 数据载荷
- CRC (4B): CRC32校验

### 2.2 命令定义

#### 2.2.1 系统控制命令 (0x01-0x0F)
| 命令ID | 命令名称 | 数据格式 | 说明 |
|--------|----------|----------|------|
| 0x01 | SYSTEM_INIT | - | 系统初始化 |
| 0x02 | SYSTEM_START | mode(1B) | 启动系统 |
| 0x03 | SYSTEM_STOP | - | 停止系统 |
| 0x04 | SYSTEM_RESET | - | 系统复位 |
| 0x05 | HEARTBEAT | timestamp(4B) | 心跳包 |
| 0x06 | STATUS_REQ | - | 状态查询 |
| 0x07 | STATUS_RSP | status_data(20B) | 状态响应 |
| 0x08 | ERROR_REPORT | error_code(2B), desc(18B) | 错误报告 |

#### 2.2.2 射频控制命令 (0x10-0x2F)
| 命令ID | 命令名称 | 数据格式 | 说明 |
|--------|----------|----------|------|
| 0x10 | RF_FREQ_SET | freq(4B) | 设置频率 |
| 0x11 | RF_POWER_SET | power(2B) | 设置功率 |
| 0x12 | RF_MODE_SET | mode(1B) | 设置工作模式 |
| 0x13 | RF_START_TX | duration(4B) | 开始发射 |
| 0x14 | RF_STOP_TX | - | 停止发射 |
| 0x15 | RF_SCAN_START | start_freq(4B), end_freq(4B) | 开始扫描 |
| 0x16 | RF_SCAN_STOP | - | 停止扫描 |
| 0x17 | RF_SCAN_DATA | freq(4B), rssi(2B) | 扫描数据 |

#### 2.2.3 训练控制命令 (0x30-0x4F)
| 命令ID | 命令名称 | 数据格式 | 说明 |
|--------|----------|----------|------|
| 0x30 | TRAIN_MODE_SET | mode(1B), params(20B) | 设置训练模式 |
| 0x31 | TRAIN_START | scenario_id(2B) | 开始训练 |
| 0x32 | TRAIN_STOP | - | 停止训练 |
| 0x33 | TRAIN_PAUSE | - | 暂停训练 |
| 0x34 | TRAIN_RESUME | - | 恢复训练 |
| 0x35 | TRAIN_DATA | data_type(1B), data(20B) | 训练数据 |

### 2.3 通信流程

#### 2.3.1 系统启动流程
```
主控                          副控
  |                            |
  |-------- SYSTEM_INIT ------>|
  |<------- STATUS_RSP --------|
  |                            |
  |-------- SYSTEM_START ----->|
  |<------- STATUS_RSP --------|
  |                            |
  |<------- HEARTBEAT ---------|
  |-------- HEARTBEAT -------->|
```

#### 2.3.2 射频控制流程
```
主控                          副控
  |                            |
  |------- RF_FREQ_SET ------->|
  |<------ STATUS_RSP ---------|
  |                            |
  |------- RF_POWER_SET ------>|
  |<------ STATUS_RSP ---------|
  |                            |
  |------- RF_START_TX ------->|
  |<------ STATUS_RSP ---------|
```

## 3. 模块间通信协议

### 3.1 I2C通信协议

#### 3.1.1 MCP4725 DAC控制
```
设备地址: 0x60 (默认)
写命令格式:
+--------+--------+--------+
| DevAddr| Data_H | Data_L |
+--------+--------+--------+
|  0x60  | D11-D4 | D3-D0  |

数据范围: 0-4095 (12位)
电压输出: (Data/4095) × Vref
```

#### 3.1.2 OLED显示控制
```
设备地址: 0x3C (SSD1315)
命令格式:
+--------+--------+--------+
| DevAddr| Control| Data   |
+--------+--------+--------+
|  0x3C  |  0x00  | Command|
|  0x3C  |  0x40  | Data   |

常用命令:
- 0xAE: 显示关闭
- 0xAF: 显示开启
- 0x20: 内存地址模式
- 0x21: 列地址设置
- 0x22: 页地址设置
```

### 3.2 SPI通信协议

#### 3.2.1 NRF24L01配置
```
寄存器访问格式:
读命令: R_REGISTER | reg_addr
写命令: W_REGISTER | reg_addr

关键寄存器:
- 0x00: CONFIG (配置寄存器)
- 0x01: EN_AA (自动应答使能)
- 0x02: EN_RXADDR (接收地址使能)
- 0x03: SETUP_AW (地址宽度设置)
- 0x04: SETUP_RETR (重传设置)
- 0x05: RF_CH (射频通道)
- 0x06: RF_SETUP (射频设置)
```

### 3.3 串口通信协议

#### 3.3.1 TJC串口屏协议
```
命令格式: 指令内容 + 0xFF + 0xFF + 0xFF

常用指令:
- page 0: 切换到页面0
- t0.txt="Hello": 设置文本框内容
- n0.val=123: 设置数值框内容
- vis t0,1: 设置控件可见性
- get t0.txt: 获取文本框内容

响应格式:
- 0x65: 触摸事件
- 0x66: 页面切换完成
- 0x67: 触摸坐标
- 0x68: 字符串数据返回
```

## 4. 错误处理机制

### 4.1 通信错误处理
```
错误类型及处理:
1. 超时错误: 重传机制 (最多3次)
2. CRC错误: 请求重传
3. 序列号错误: 丢弃数据包
4. 长度错误: 丢弃数据包
5. 连接断开: 自动重连
```

### 4.2 错误码定义
| 错误码 | 错误名称 | 说明 |
|--------|----------|------|
| 0x0000 | NO_ERROR | 无错误 |
| 0x0001 | COMM_TIMEOUT | 通信超时 |
| 0x0002 | COMM_CRC_ERROR | CRC校验错误 |
| 0x0003 | COMM_SEQ_ERROR | 序列号错误 |
| 0x0010 | RF_INIT_ERROR | 射频初始化错误 |
| 0x0011 | RF_FREQ_ERROR | 频率设置错误 |
| 0x0012 | RF_POWER_ERROR | 功率设置错误 |
| 0x0020 | DAC_ERROR | DAC模块错误 |
| 0x0021 | DISPLAY_ERROR | 显示模块错误 |

## 5. 性能参数

### 5.1 通信性能
- 数据传输速率: 2Mbps (NRF24L01)
- 通信距离: 100m (开阔环境)
- 数据包大小: 32字节
- 传输延迟: <10ms
- 丢包率: <0.1%

### 5.2 实时性要求
- 心跳间隔: 1秒
- 状态更新: 100ms
- 射频控制响应: <50ms
- 显示更新: 200ms
- 紧急停止响应: <10ms
