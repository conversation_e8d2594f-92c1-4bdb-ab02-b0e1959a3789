/*
 * DHT11传感器简单测试程序
 * 用于验证DHT11传感器连接是否正确
 */

#include "DHT.h"

// DHT11传感器配置
#define DHT_PIN 8
#define DHT_TYPE DHT11

// 创建DHT实例
DHT dht(DHT_PIN, DHT_TYPE);

void setup() {
    Serial.begin(115200);
    Serial.println("=== DHT11传感器连接测试 ===");
    
    // 初始化DHT传感器
    dht.begin();
    Serial.println("DHT11传感器初始化完成");
    Serial.println("等待2秒后开始读取...");
    delay(2000);
}

void loop() {
    Serial.println("--- 开始读取DHT11 ---");
    
    // 读取湿度
    float humidity = dht.readHumidity();
    Serial.print("湿度读取结果: ");
    if(isnan(humidity)) {
        Serial.println("失败 (NaN)");
    } else {
        Serial.print(humidity);
        Serial.println("%");
    }
    
    // 读取温度
    float temperature = dht.readTemperature();
    Serial.print("温度读取结果: ");
    if(isnan(temperature)) {
        Serial.println("失败 (NaN)");
    } else {
        Serial.print(temperature);
        Serial.println("°C");
    }
    
    // 综合判断
    if(isnan(humidity) || isnan(temperature)) {
        Serial.println("❌ DHT11传感器读取失败!");
        Serial.println("请检查:");
        Serial.println("1. 引脚连接 (VCC→5V, DATA→D8, GND→GND)");
        Serial.println("2. 上拉电阻 (4.7kΩ在5V和D8之间)");
        Serial.println("3. 传感器是否损坏");
        Serial.println("4. 面包板连接是否牢固");
    } else {
        Serial.println("✅ DHT11传感器工作正常!");
        
        // 计算体感温度 (可选)
        float heatIndex = dht.computeHeatIndex(temperature, humidity, false);
        Serial.print("体感温度: ");
        Serial.print(heatIndex);
        Serial.println("°C");
    }
    
    Serial.println("========================");
    Serial.println();
    
    // 等待3秒再次读取 (DHT11建议至少1秒间隔)
    delay(3000);
}
