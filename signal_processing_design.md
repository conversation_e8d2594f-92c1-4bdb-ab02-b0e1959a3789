# 信号处理模块设计

## 1. 信号处理系统概述

### 1.1 系统架构
```
数字控制层 (Arduino MEGA 2560)
    ↓ (I2C/PWM/GPIO)
模拟信号生成层 (MCP4725 DAC + VCO)
    ↓ (RF Signal)
射频处理链 (滤波器 + 功率放大器)
    ↓ (Amplified RF)
天线输出 (2.4G信号发射)
```

### 1.2 技术指标 (基于实际硬件)
- **工作频段**: 2402-2461 MHz (受滤波器限制)
- **频率精度**: ±1 MHz
- **VCO输出功率**: 7.36-8.05 dBm (实测数据)
- **系统最大输出**: 30 dBm (1W，PA模块)
- **调制带宽**: 59 MHz (滤波器带宽)
- **响应时间**: <10 ms
- **频率稳定度**: ±10 ppm

## 2. VCO压控振荡器控制

### 2.1 VCO特性分析
```
典型VCO参数 (如MAX2606):
- 工作频段: 2400-2500 MHz
- 控制电压: 0.5-4.5V
- 频率灵敏度: ~25 MHz/V
- 相位噪声: -90 dBc/Hz @ 100kHz
- 功耗: 16 mA @ 5V
```

### 2.2 频率控制算法
```cpp
class VCOController {
private:
    MCP4725 dac;
    uint32_t targetFreq;
    uint16_t currentDACValue;
    
    // 频率-电压查找表
    struct FreqVoltageMap {
        uint32_t frequency;  // MHz
        uint16_t dacValue;   // 12-bit DAC值
    };
    
    // 基于实际VCO测试数据的频率-电压查找表
    FreqVoltageMap freqTable[60] = {
        {2402, 0},     // 2402MHz -> 0V (外推)
        {2421, 409},   // 2421MHz -> 1.0V (实测)
        {2429, 450},   // 2429MHz -> 1.1V (实测)
        {2436, 491},   // 2436MHz -> 1.2V (实测)
        {2444, 532},   // 2444MHz -> 1.3V (实测)
        {2452, 573},   // 2452MHz -> 1.4V (实测)
        // ... 基于实测数据插值
        {2461, 655}    // 2461MHz -> 1.6V (外推)
    };
    
public:
    void init();
    bool setFrequency(uint32_t freq);
    uint32_t getCurrentFrequency();
    void calibrateVCO();
    uint16_t frequencyToDAC(uint32_t freq);
};

// 频率设置实现
bool VCOController::setFrequency(uint32_t freq) {
    if(freq < 2402 || freq > 2461) {
        return false; // 频率超出滤波器范围
    }
    
    uint16_t dacValue = frequencyToDAC(freq);
    dac.setVoltage(dacValue);
    
    targetFreq = freq;
    currentDACValue = dacValue;
    
    delay(5); // 等待VCO稳定
    return true;
}

// 线性插值计算DAC值
uint16_t VCOController::frequencyToDAC(uint32_t freq) {
    // 在查找表中找到相邻的两个点
    for(int i = 0; i < 100; i++) {
        if(freq >= freqTable[i].frequency && 
           freq <= freqTable[i+1].frequency) {
            
            // 线性插值
            uint32_t f1 = freqTable[i].frequency;
            uint32_t f2 = freqTable[i+1].frequency;
            uint16_t d1 = freqTable[i].dacValue;
            uint16_t d2 = freqTable[i+1].dacValue;
            
            return d1 + (freq - f1) * (d2 - d1) / (f2 - f1);
        }
    }
    return 2048; // 默认中间值
}
```

## 3. 带通滤波器控制

### 3.1 滤波器设计
```
多段式带通滤波器:
- 低段: 2400-2430 MHz
- 中段: 2430-2470 MHz  
- 高段: 2470-2500 MHz

控制方式:
- GPIO控制继电器切换
- 3位控制码选择8个滤波器组合
```

### 3.2 滤波器控制实现
```cpp
class FilterController {
private:
    uint8_t filterPins[3] = {7, 8, 9}; // GPIO控制引脚
    uint8_t currentFilter;
    
    struct FilterBand {
        uint32_t startFreq;
        uint32_t endFreq;
        uint8_t controlCode;
        float insertionLoss; // dB
    };
    
    FilterBand filterBands[8] = {
        {2400, 2410, 0b000, 1.5},
        {2410, 2420, 0b001, 1.3},
        {2420, 2430, 0b010, 1.2},
        {2430, 2440, 0b011, 1.1},
        {2440, 2450, 0b100, 1.0},
        {2450, 2460, 0b101, 1.1},
        {2460, 2480, 0b110, 1.2},
        {2480, 2500, 0b111, 1.4}
    };
    
public:
    void init();
    bool selectFilter(uint32_t frequency);
    uint8_t getOptimalFilter(uint32_t freq);
    float getInsertionLoss();
};

bool FilterController::selectFilter(uint32_t frequency) {
    uint8_t filterCode = getOptimalFilter(frequency);
    
    // 设置GPIO控制滤波器
    for(int i = 0; i < 3; i++) {
        digitalWrite(filterPins[i], (filterCode >> i) & 0x01);
    }
    
    currentFilter = filterCode;
    delay(10); // 等待继电器切换
    return true;
}
```

## 4. 功率放大器控制

### 4.1 功放特性
```
典型功放参数:
- 工作频段: 2400-2500 MHz
- 增益: 20-30 dB (可调)
- 最大输出: +20 dBm
- 控制方式: PWM电压控制
- 效率: 35-45%
```

### 4.2 功率控制实现
```cpp
class AmplifierController {
private:
    uint8_t powerControlPin = 6; // PWM输出引脚
    uint16_t currentPowerSetting;
    float targetPower_dBm;
    
    // 功率校准表
    struct PowerCalibration {
        float power_dBm;
        uint16_t pwmValue;
        float actualPower_dBm;
    };
    
    PowerCalibration powerTable[21] = {
        {0.0,  51,  0.2},   // 0dBm目标
        {1.0,  64,  1.1},   // 1dBm目标
        // ... 校准数据
        {20.0, 255, 19.8}   // 20dBm目标
    };
    
public:
    void init();
    bool setPower(float power_dBm);
    float getCurrentPower();
    void calibratePower();
    uint16_t powerToPWM(float power_dBm);
};

bool AmplifierController::setPower(float power_dBm) {
    if(power_dBm < 0 || power_dBm > 20) {
        return false; // 功率超出范围
    }
    
    uint16_t pwmValue = powerToPWM(power_dBm);
    analogWrite(powerControlPin, pwmValue);
    
    targetPower_dBm = power_dBm;
    currentPowerSetting = pwmValue;
    
    delay(5); // 等待功放稳定
    return true;
}
```

## 5. 干扰信号生成算法

### 5.1 噪声干扰生成
```cpp
class NoiseJamming {
private:
    uint32_t centerFreq;
    uint32_t bandwidth;
    uint16_t noiseLevel;
    
public:
    void generateNoise() {
        // 伪随机频率跳变
        for(int i = 0; i < 100; i++) {
            uint32_t randomFreq = centerFreq + 
                (random(-bandwidth/2, bandwidth/2));
            
            vcoController.setFrequency(randomFreq);
            delay(10); // 驻留时间
        }
    }
    
    void setParameters(uint32_t freq, uint32_t bw, uint16_t level) {
        centerFreq = freq;
        bandwidth = bw;
        noiseLevel = level;
    }
};
```

### 5.2 扫频干扰生成
```cpp
class SweepJamming {
private:
    uint32_t startFreq;
    uint32_t endFreq;
    uint32_t sweepRate; // MHz/s
    bool sweepDirection; // true: 上扫, false: 下扫
    
public:
    void generateSweep() {
        uint32_t currentFreq = sweepDirection ? startFreq : endFreq;
        uint32_t targetFreq = sweepDirection ? endFreq : startFreq;
        int32_t step = sweepDirection ? 1 : -1;
        
        while(currentFreq != targetFreq) {
            vcoController.setFrequency(currentFreq);
            
            // 计算延时以达到指定扫描速率
            uint32_t delayTime = 1000 / sweepRate; // ms
            delay(delayTime);
            
            currentFreq += step;
        }
        
        sweepDirection = !sweepDirection; // 反向扫描
    }
    
    void setSweepParameters(uint32_t start, uint32_t end, uint32_t rate) {
        startFreq = start;
        endFreq = end;
        sweepRate = rate;
    }
};
```

### 5.3 脉冲干扰生成
```cpp
class PulseJamming {
private:
    uint32_t pulseWidth_ms;
    uint32_t pulseInterval_ms;
    uint16_t pulsePower;
    bool pulseState;
    
public:
    void generatePulse() {
        static uint32_t lastPulseTime = 0;
        uint32_t currentTime = millis();
        
        if(!pulseState) {
            // 脉冲关闭状态
            if(currentTime - lastPulseTime >= pulseInterval_ms) {
                // 开启脉冲
                amplifierController.setPower(pulsePower);
                pulseState = true;
                lastPulseTime = currentTime;
            }
        } else {
            // 脉冲开启状态
            if(currentTime - lastPulseTime >= pulseWidth_ms) {
                // 关闭脉冲
                amplifierController.setPower(0);
                pulseState = false;
                lastPulseTime = currentTime;
            }
        }
    }
    
    void setPulseParameters(uint32_t width, uint32_t interval, uint16_t power) {
        pulseWidth_ms = width;
        pulseInterval_ms = interval;
        pulsePower = power;
    }
};
```

### 5.4 欺骗干扰生成
```cpp
class DeceptionJamming {
private:
    uint8_t signalType; // WiFi, 蓝牙等
    uint8_t deceptionMode;
    
public:
    void generateWiFiDeception() {
        // 模拟WiFi信标帧
        uint32_t wifiChannels[] = {2412, 2437, 2462}; // 1, 6, 11信道
        
        for(int i = 0; i < 3; i++) {
            vcoController.setFrequency(wifiChannels[i]);
            
            // 发送虚假信标
            generateBeaconFrame();
            delay(100);
        }
    }
    
    void generateBluetoothDeception() {
        // 蓝牙跳频序列模拟
        for(int i = 0; i < 79; i++) {
            uint32_t btFreq = 2402 + i; // 蓝牙频点
            vcoController.setFrequency(btFreq);
            
            // 发送虚假蓝牙包
            generateBTPacket();
            delay(1); // 蓝牙时隙625μs
        }
    }
    
private:
    void generateBeaconFrame() {
        // WiFi信标帧结构模拟
        // 这里简化为功率调制
        for(int i = 0; i < 10; i++) {
            amplifierController.setPower(10 + (i % 3));
            delayMicroseconds(100);
        }
    }
    
    void generateBTPacket() {
        // 蓝牙包结构模拟
        amplifierController.setPower(5);
        delayMicroseconds(366); // 蓝牙包长度
        amplifierController.setPower(0);
    }
};
```

## 6. 信号监测与反馈

### 6.1 功率监测
```cpp
class PowerMonitor {
private:
    uint8_t powerDetectorPin = A0;
    float calibrationFactor = 50.0; // mV/dBm
    
public:
    float measureOutputPower() {
        uint16_t adcValue = analogRead(powerDetectorPin);
        float voltage_mV = (adcValue * 5000.0) / 1024.0;
        
        // 转换为dBm
        float power_dBm = (voltage_mV - 1000) / calibrationFactor;
        return power_dBm;
    }
    
    bool isPowerInRange(float targetPower, float tolerance = 1.0) {
        float measuredPower = measureOutputPower();
        return abs(measuredPower - targetPower) <= tolerance;
    }
};
```

### 6.2 温度监测
```cpp
class TemperatureMonitor {
private:
    uint8_t tempSensorPin = A1;
    
public:
    float measureTemperature() {
        uint16_t adcValue = analogRead(tempSensorPin);
        float voltage = (adcValue * 5.0) / 1024.0;
        
        // LM35温度传感器: 10mV/°C
        float temperature = voltage * 100.0;
        return temperature;
    }
    
    bool isTemperatureNormal() {
        float temp = measureTemperature();
        return temp < 60.0; // 60°C阈值
    }
};
```

## 7. 信号处理主控制器

### 7.1 集成控制类
```cpp
class SignalProcessor {
private:
    VCOController vco;
    FilterController filter;
    AmplifierController amplifier;
    PowerMonitor powerMon;
    TemperatureMonitor tempMon;
    
    NoiseJamming noiseJam;
    SweepJamming sweepJam;
    PulseJamming pulseJam;
    DeceptionJamming deceptionJam;
    
    JammingMode currentMode;
    bool transmissionActive;
    
public:
    void init();
    void setJammingMode(JammingMode mode);
    void startTransmission();
    void stopTransmission();
    void updateSignalProcessing();
    void performSafetyCheck();
};

void SignalProcessor::updateSignalProcessing() {
    // 安全检查
    performSafetyCheck();
    
    if(!transmissionActive) return;
    
    // 根据模式执行相应的信号处理
    switch(currentMode) {
        case JAMMING_NOISE:
            noiseJam.generateNoise();
            break;
        case JAMMING_SWEEP:
            sweepJam.generateSweep();
            break;
        case JAMMING_PULSE:
            pulseJam.generatePulse();
            break;
        case JAMMING_DECEPTION:
            deceptionJam.generateWiFiDeception();
            break;
    }
}

void SignalProcessor::performSafetyCheck() {
    // 温度检查
    if(!tempMon.isTemperatureNormal()) {
        // 降低功率或停止发射
        amplifier.setPower(amplifier.getCurrentPower() * 0.8);
    }
    
    // 功率检查
    float targetPower = amplifier.getCurrentPower();
    if(!powerMon.isPowerInRange(targetPower)) {
        // 功率异常，记录错误
        // errorHandler.reportError(POWER_ERROR);
    }
}
```
