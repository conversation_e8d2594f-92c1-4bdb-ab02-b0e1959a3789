/*
 * 用户界面管理器实现 - TJC触摸屏和OLED显示控制
 */

#include "UIManager.h"
#include "SystemManager.h"
#include "CommManager.h"

// 构造函数
UIManager::UIManager() : 
    tjcSerial(TJC_SERIAL_RX, TJC_SERIAL_TX),
    oled(OLED_WIDTH, OLED_HEIGHT, &Wire, -1),
    currentPage(PAGE_STARTUP),
    previousPage(PAGE_STARTUP),
    touchLocked(false),
    lastTouchTime(0),
    currentOLEDDisplay(OLED_SYSTEM_STATUS),
    lastOLEDUpdate(0),
    systemInitialized(false),
    initProgress(0),
    touchEventPending(false)
{
}

// 初始化函数
bool UIManager::init() {
    DEBUG_PRINTLN(F("初始化用户界面管理器..."));
    
    // 初始化TJC串口屏
    if(!initTJCDisplay()) {
        DEBUG_PRINTLN(F("TJC显示屏初始化失败"));
        return false;
    }
    
    // 初始化OLED显示屏
    if(!initOLED()) {
        DEBUG_PRINTLN(F("OLED显示屏初始化失败"));
        return false;
    }
    
    // 显示启动画面
    showStartupScreen();
    
    DEBUG_PRINTLN(F("用户界面管理器初始化完成"));
    return true;
}

// 初始化TJC显示屏
bool UIManager::initTJCDisplay() {
    // 启动TJC串口通信
    tjcSerial.begin(TJC_SERIAL_BAUD);

    // 等待TJC屏幕完全启动 (解决启动同步问题)
    DEBUG_PRINTLN(F("等待TJC屏幕启动..."));
    delay(3000);  // 增加等待时间到3秒

    // 清空串口缓冲区
    while(tjcSerial.available()) {
        tjcSerial.read();
    }

    // 多次尝试建立通信
    for(int attempt = 0; attempt < 5; attempt++) {
        DEBUG_PRINT(F("TJC通信尝试 "));
        DEBUG_PRINTLN(attempt + 1);

        // 发送页面跳转命令测试通信
        sendTJCCommand("page startup");
        delay(300);

        // 发送简单命令测试
        sendTJCCommand("page main");
        delay(300);

        // 如果能发送命令就认为通信正常
        DEBUG_PRINTLN(F("TJC显示屏通信建立"));
        return true;
    }

    DEBUG_PRINTLN(F("TJC显示屏通信失败"));
    return false;
}

// 初始化OLED显示屏
bool UIManager::initOLED() {
    // 初始化I2C
    Wire.begin();
    
    // 初始化OLED
    if(!oled.begin(SSD1306_SWITCHCAPVCC, OLED_I2C_ADDRESS)) {
        DEBUG_PRINTLN(F("OLED初始化失败"));
        return false;
    }
    
    // 清屏并显示启动信息
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);
    oled.println(F("ECM Training System"));
    oled.println(F("OLED Display Ready"));
    oled.display();
    
    DEBUG_PRINTLN(F("OLED初始化成功"));
    return true;
}

// 发送TJC命令 (符合官方规范)
void UIManager::sendTJCCommand(const String& command) {
    tjcSerial.print(command);
    tjcSerial.write(0xff);
    tjcSerial.write(0xff);
    tjcSerial.write(0xff);

    DEBUG_PRINT(F("TJC命令: "));
    DEBUG_PRINTLN(command);
}

// 发送TJC文本 (官方格式：控件名.txt="文本")
void UIManager::sendTJCText(const String& component, const String& text) {
    String command = component + ".txt=\"" + text + "\"";
    sendTJCCommand(command);
}

// 发送TJC数值 (官方格式：控件名.val=数值)
void UIManager::sendTJCValue(const String& component, int value) {
    String command = component + ".val=" + String(value);
    sendTJCCommand(command);
}

// 发送TJC数值
void UIManager::sendTJCValue(const String& component, int value) {
    String command = component + ".val=" + String(value);
    sendTJCCommand(command);
}

// 发送TJC文本
void UIManager::sendTJCText(const String& component, const String& text) {
    String command = component + ".txt=\"" + text + "\"";
    sendTJCCommand(command);
}

// 读取TJC响应
bool UIManager::readTJCResponse(String& response) {
    unsigned long startTime = millis();
    response = "";
    
    while(millis() - startTime < 1000) { // 1秒超时
        if(tjcSerial.available()) {
            char c = tjcSerial.read();
            if(c == 0xff) {
                // 检查是否是完整的结束符
                if(tjcSerial.available() >= 2) {
                    if(tjcSerial.read() == 0xff && tjcSerial.read() == 0xff) {
                        return true;
                    }
                }
            } else {
                response += c;
            }
        }
        delay(1);
    }
    return false;
}

// 切换TJC页面
void UIManager::switchTJCPage(UIPage page) {
    previousPage = currentPage;
    currentPage = page;
    
    String command = "page " + String((int)page);
    sendTJCCommand(command);
    
    DEBUG_PRINT(F("切换到页面: "));
    DEBUG_PRINTLN((int)page);
}

// 显示启动画面
void UIManager::showStartupScreen() {
    switchTJCPage(PAGE_STARTUP);
    
    // 设置启动画面文本
    sendTJCText("t0", "ECM TRAINING SYSTEM");
    sendTJCText("t1", "便携式电子对抗模拟训练系统");
    sendTJCText("t2", "Version 2.0");
    sendTJCText("t3", "正在初始化系统...");
    
    // 初始化进度条
    sendTJCValue("j0", 0);
    
    // 更新OLED显示
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setCursor(0, 0);
    oled.println(F("ECM Training System"));
    oled.println(F("Version 2.0"));
    oled.println();
    oled.println(F("Initializing..."));
    oled.display();
}

// 更新启动进度
void UIManager::showProgress(uint8_t percentage, const String& text) {
    initProgress = percentage;
    
    // 更新TJC进度条
    sendTJCValue("j0", percentage);
    
    // 更新状态文本
    if(text.length() > 0) {
        sendTJCText("t3", text);
    }
    
    // 更新OLED显示
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setCursor(0, 0);
    oled.println(F("ECM Training System"));
    oled.println(F("Version 2.0"));
    oled.println();
    oled.print(F("Progress: "));
    oled.print(percentage);
    oled.println(F("%"));
    if(text.length() > 0) {
        oled.println(text);
    }
    oled.display();
}

// 显示主菜单
void UIManager::showMainMenu() {
    switchTJCPage(PAGE_MAIN_MENU);
    systemInitialized = true;
    
    // 设置主菜单标题
    sendTJCText("t0", "电子对抗模拟训练系统");
    
    // 更新状态栏
    updateMainMenuStatus();
    
    // 更新OLED显示系统状态
    setOLEDDisplay(OLED_SYSTEM_STATUS);
}

// 更新主菜单状态
void UIManager::updateMainMenuStatus() {
    String statusText = "[OK] 系统就绪  |  ";

    // 副控连接状态
    if(commMgr.isSlaveConnected()) {
        statusText += "[LINK] 副控在线  |  ";
    } else {
        statusText += "[LINK] 副控离线  |  ";
    }

    // 温度显示
    int8_t temp = systemMgr.getTemperature();
    statusText += "TEMP: " + String(temp) + "C  |  ";

    // 时间显示
    unsigned long uptime = systemMgr.getUptime();
    int hours = uptime / 3600000;
    int minutes = (uptime % 3600000) / 60000;
    statusText += "TIME: " + String(hours) + ":" + (minutes < 10 ? "0" : "") + String(minutes);
    
    sendTJCText("t1", statusText);
    
    // 更新电池图标
    uint16_t batteryVoltage = systemMgr.getBatteryVoltage();
    uint8_t batteryPercent = map(batteryVoltage, 6000, 8400, 0, 100);
    batteryPercent = constrain(batteryPercent, 0, 100);
    
    sendTJCValue("j1", batteryPercent); // 电池进度条
    sendTJCText("t2", String(batteryPercent) + "%");
}

// 主循环更新
void UIManager::update() {
    // 处理触摸事件
    handleTouchEvent();
    
    // 更新OLED显示
    updateOLEDDisplay();
    
    // 定期更新界面状态
    static unsigned long lastUIUpdate = 0;
    if(millis() - lastUIUpdate >= UI_UPDATE_INTERVAL) {
        lastUIUpdate = millis();
        
        if(currentPage == PAGE_MAIN_MENU) {
            updateMainMenuStatus();
        }
    }
    
    // 检查触摸锁定超时
    if(touchLocked && millis() - lastTouchTime > TOUCH_LOCK_TIMEOUT) {
        touchLocked = false;
        DEBUG_PRINTLN(F("触摸锁定超时解除"));
    }
}

// 优化的触摸事件处理 (解决点击无反应问题)
void UIManager::handleTouchEvent() {
    // 处理所有可用的串口数据
    while(tjcSerial.available() > 0) {
        // 查找滑块事件数据包
        if(tjcSerial.available() >= 4) {
            uint8_t buffer[4];
            tjcSerial.readBytes(buffer, 4);

            if(buffer[0] == 0x55 && buffer[1] == 0xAA) {
                uint8_t sliderType = buffer[2];
                uint8_t sliderValue = buffer[3];

                DEBUG_PRINT(F("滑块事件: 类型="));
                DEBUG_PRINT(sliderType);
                DEBUG_PRINT(F(", 值="));
                DEBUG_PRINTLN(sliderValue);

                processSliderInput(sliderType, sliderValue);
                lastTouchTime = millis();

                // 处理完一个事件后继续检查是否还有数据
                continue;
            } else {
                // 如果不是有效的滑块数据包，丢弃第一个字节，继续查找
                DEBUG_PRINT(F("丢弃无效数据: "));
                for(int i = 0; i < 4; i++) {
                    DEBUG_PRINT(buffer[i], HEX);
                    DEBUG_PRINT(F(" "));
                }
                DEBUG_PRINTLN();
            }
        } else {
            // 数据不足4字节，等待更多数据或清除
            delay(10);
            if(tjcSerial.available() < 4) {
                // 清除不完整的数据
                while(tjcSerial.available() > 0) {
                    tjcSerial.read();
                }
                break;
            }
        }
    }

    // 检测当前页面变化 (通过定期查询)
    static unsigned long lastPageCheck = 0;
    if(millis() - lastPageCheck >= 500) { // 每500ms检查一次
        lastPageCheck = millis();
        checkCurrentPage();
    }
}

// 处理触摸输入
bool UIManager::processTouchInput() {
    // 这个函数由handleTouchEvent调用
    return true;
}

// 检测当前页面 (简化方案)
void UIManager::checkCurrentPage() {
    // 发送查询当前页面命令
    sendTJCCommand("sendme");

    // 读取响应来判断当前页面
    String response;
    if(readTJCResponse(response)) {
        // 根据响应更新当前页面状态
        updateCurrentPageStatus(response);
    }
}

// 更新当前页面状态
void UIManager::updateCurrentPageStatus(const String& response) {
    // 根据当前页面执行相应的更新操作
    if(response.indexOf("main") >= 0) {
        if(currentPage != PAGE_MAIN_MENU) {
            currentPage = PAGE_MAIN_MENU;
            updateMainMenuStatus();
            setOLEDDisplay(OLED_SYSTEM_STATUS);
        }
    } else if(response.indexOf("recon") >= 0) {
        if(currentPage != PAGE_SIGNAL_RECON) {
            currentPage = PAGE_SIGNAL_RECON;
            setOLEDDisplay(OLED_SIGNAL_ANALYSIS);
        }
    } else if(response.indexOf("jamming") >= 0) {
        if(currentPage != PAGE_JAMMING_TRAINING) {
            currentPage = PAGE_JAMMING_TRAINING;
            setOLEDDisplay(OLED_JAMMING_EFFECT);
        }
    } else if(response.indexOf("data") >= 0) {
        if(currentPage != PAGE_DATA_ANALYSIS) {
            currentPage = PAGE_DATA_ANALYSIS;
            setOLEDDisplay(OLED_SYSTEM_STATUS);
        }
    } else if(response.indexOf("settings") >= 0) {
        if(currentPage != PAGE_SYSTEM_SETTINGS) {
            currentPage = PAGE_SYSTEM_SETTINGS;
            setOLEDDisplay(OLED_SYSTEM_STATUS);
        }
    }
}

// 简化：移除复杂的触摸处理函数
// 页面切换现在由TJC屏幕直接处理，Arduino只需要更新显示内容

// 显示测试
bool UIManager::testDisplay() {
    DEBUG_PRINTLN(F("开始显示测试..."));
    
    // 测试TJC显示
    sendTJCCommand("page 0");
    delay(1000);
    sendTJCText("t0", "Display Test");
    
    // 测试OLED显示
    oled.clearDisplay();
    oled.setTextSize(2);
    oled.setCursor(0, 0);
    oled.println(F("TEST"));
    oled.display();
    
    DEBUG_PRINTLN(F("显示测试完成"));
    return true;
}

// 显示信号侦察界面
void UIManager::showSignalRecon() {
    switchTJCPage(PAGE_SIGNAL_RECON);

    // 设置页面标题
    sendTJCText("t20", "[RECON] 信号侦察");

    // 初始化侦察界面
    sendTJCText("t21", "[SCAN] 准备就绪");
    sendTJCText("t22", "扫描模式: 全频段");
    sendTJCValue("h20", 50); // 扫描速度滑块

    // 清空检测结果
    sendTJCText("t23", "[WAIT] 等待检测信号...");

    // 更新OLED显示
    setOLEDDisplay(OLED_SIGNAL_ANALYSIS);
}

// 显示干扰训练界面
void UIManager::showJammingTraining() {
    switchTJCPage(PAGE_JAMMING_TRAINING);

    // 设置页面标题
    sendTJCText("t30", "[JAM] 干扰训练进行中");

    // 初始化目标信息
    sendTJCText("t31", "[TARGET]\rFreq: 2412MHz\rPower: -45dBm\rQuality: 85%");

    // 初始化干扰参数
    sendTJCText("t32", "[JAMMING]\rFreq: 2435MHz\rPower: 15dBm\rMode: NOISE");

    // 初始化效果监控
    sendTJCText("t33", "[EFFECT] 干扰效果监控\r中断率: 0%  丢包率: 0%\r信号衰减: 0dB  效率: 0%");

    // 初始化控制滑块
    sendTJCValue("h30", 15); // 功率滑块
    sendTJCValue("h31", 2435); // 频率滑块

    // 初始化进度条
    sendTJCValue("j30", 0); // 中断率
    sendTJCValue("j31", 0); // 丢包率
    sendTJCValue("j32", 0); // 信号衰减
    sendTJCValue("j33", 0); // 干扰效率

    // 更新OLED显示
    setOLEDDisplay(OLED_JAMMING_EFFECT);
}

// 显示数据分析界面
void UIManager::showDataAnalysis() {
    switchTJCPage(PAGE_DATA_ANALYSIS);

    // 设置页面标题
    sendTJCText("t40", "[DATA] 训练数据分析");

    // 显示会话概览
    sendTJCText("t41", "Date: 2024-01-16  Duration: 05:23  Mode: NOISE\rScore: A (92)  Success: 85%");

    // 显示统计数据
    sendTJCText("t42", "Avg Power: 12.5dBm  Max Power: 18.2dBm\rResponse: 15ms  Stability: 98%\rPackets: 1250  Accuracy: 92%");

    // 更新OLED显示
    setOLEDDisplay(OLED_SYSTEM_STATUS);
}

// 显示系统设置界面
void UIManager::showSystemSettings() {
    switchTJCPage(PAGE_SYSTEM_SETTINGS);

    // 设置页面标题
    sendTJCText("t50", "[SET] 系统设置");

    // 射频参数区
    sendTJCText("t51", "[RF PARAMS]\rFreq: 2402-2461MHz\rDefault Power: 10dBm\rScan Speed: Medium\rSafety: Enabled");

    // 系统配置区
    sendTJCText("t52", "[SYSTEM]\rBeeper: ON\rBrightness: 80%\rTemp Unit: C\rTime Format: 24H");

    // 训练参数区
    sendTJCText("t53", "[TRAINING]\rDefault Duration: 60s\rAuto Save: ON\rData Limit: 100\rAlgorithm: Standard");

    // 关于系统区
    sendTJCText("t54", "[ABOUT]\rVersion: v2.0.0\rHardware: UNO R4\rMemory: 1.2/2.0KB\rUptime: 02:15:30");

    // 初始化滑块
    sendTJCValue("h50", 10); // 默认功率
    sendTJCValue("h51", 80); // 背光亮度
}

// 显示错误界面
void UIManager::showError(uint16_t errorCode) {
    switchTJCPage(PAGE_ERROR_DISPLAY);

    // 设置错误标题
    sendTJCText("t60", "[ERROR] 系统错误");

    // 显示错误代码
    sendTJCText("t61", "Error Code: 0x" + String(errorCode, HEX));

    // 显示错误描述
    String errorDesc = getErrorDescription(errorCode);
    sendTJCText("t62", errorDesc);

    // 更新OLED显示错误信息
    showErrorOLED(errorCode, errorDesc);
}

// 获取错误描述
String UIManager::getErrorDescription(uint16_t errorCode) {
    switch(errorCode) {
        case ERROR_CONFIG_INIT:
            return "配置管理器初始化失败\r请检查EEPROM连接";
        case ERROR_SYSTEM_INIT:
            return "系统管理器初始化失败\r请检查硬件连接";
        case ERROR_UI_INIT:
            return "用户界面初始化失败\r请检查显示屏连接";
        case ERROR_COMM_INIT:
            return "通信管理器初始化失败\r请检查串口连接";
        case ERROR_COMM_TIMEOUT:
            return "副控通信超时\r请检查串口连接";
        case ERROR_BATTERY_LOW:
            return "电池电压过低\r请及时充电";
        case ERROR_OVER_TEMP:
            return "系统温度过高\r请检查散热";
        default:
            return "未知错误\r请联系技术支持";
    }
}

// 处理侦察界面触摸
void UIManager::handleReconTouch(uint8_t componentId) {
    switch(componentId) {
        case 10: // 返回按钮
            showMainMenu();
            break;
        case 11: // 开始侦察
            sendTJCText("t21", "[SCANNING] 正在侦察...");
            // 启动侦察功能
            break;
        case 12: // 停止侦察
            sendTJCText("t21", "[STOPPED] 侦察已停止");
            break;
        case 13: // 启动干扰
            showJammingTraining();
            break;
        default:
            DEBUG_PRINTLN(F("未知侦察界面组件"));
            break;
    }
}

// 处理训练界面触摸
void UIManager::handleTrainingTouch(uint8_t componentId) {
    switch(componentId) {
        case 30: // 返回按钮
            showMainMenu();
            break;
        case 31: // 暂停按钮
            sendTJCText("t30", "[JAM] 干扰训练已暂停");
            break;
        case 32: // 停止按钮
            sendTJCText("t30", "[JAM] 干扰训练已停止");
            showMainMenu();
            break;
        case 33: // 功率滑块
            // 处理功率调节
            break;
        case 34: // 频率滑块
            // 处理频率调节
            break;
        default:
            DEBUG_PRINTLN(F("未知训练界面组件"));
            break;
    }
}

// 处理数据界面触摸
void UIManager::handleDataTouch(uint8_t componentId) {
    switch(componentId) {
        case 40: // 返回按钮
            showMainMenu();
            break;
        case 41: // 导出按钮
            sendTJCText("t40", "[DATA] 正在导出数据...");
            break;
        case 42: // 清除按钮
            sendTJCText("t40", "[DATA] 数据已清除");
            break;
        case 43: // 上一条
            // 显示上一条记录
            break;
        case 44: // 下一条
            // 显示下一条记录
            break;
        default:
            DEBUG_PRINTLN(F("未知数据界面组件"));
            break;
    }
}

// 处理设置界面触摸
void UIManager::handleSettingsTouch(uint8_t componentId) {
    switch(componentId) {
        case 50: // 返回按钮
            showMainMenu();
            break;
        case 51: // 保存按钮
            sendTJCText("t50", "[SET] 设置已保存");
            break;
        case 52: // 重置按钮
            sendTJCText("t50", "[SET] 设置已重置");
            break;
        case 53: // 默认功率滑块
            // 处理功率设置
            break;
        case 54: // 背光滑块
            // 处理背光设置
            break;
        default:
            DEBUG_PRINTLN(F("未知设置界面组件"));
            break;
    }
}

// OLED显示控制函数
void UIManager::setOLEDDisplay(OLEDDisplayType type) {
    currentOLEDDisplay = type;
    updateOLEDDisplay();
}

void UIManager::updateOLEDDisplay() {
    static unsigned long lastUpdate = 0;
    if(millis() - lastUpdate < 200) return; // 限制更新频率
    lastUpdate = millis();

    switch(currentOLEDDisplay) {
        case OLED_JAMMING_EFFECT:
            drawJammingEffectOLED();
            break;
        case OLED_SIGNAL_ANALYSIS:
            drawSignalAnalysisOLED();
            break;
        case OLED_SYSTEM_STATUS:
            drawSystemStatusOLED();
            break;
        case OLED_ERROR_MESSAGE:
            // 错误信息已在showError中显示
            break;
    }
}

void UIManager::drawJammingEffectOLED() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setCursor(0, 0);
    oled.println(F("Jamming Effect Monitor"));
    oled.println();
    oled.println(F("Target: 2412MHz WiFi"));
    oled.println(F("Before: -45dBm  85%"));
    oled.println(F("After:  -65dBm  12%"));
    oled.println(F("Loss Rate: 78%"));
    oled.println();
    oled.println(F("Status: SUPPRESSED"));
    oled.display();
}

void UIManager::drawSignalAnalysisOLED() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setCursor(0, 0);
    oled.println(F("Signal Analysis"));
    oled.println();
    oled.println(F("Detected: WiFi 2.4G"));
    oled.println(F("Freq: 2412MHz"));
    oled.println(F("Power: -45dBm"));
    oled.println(F("Quality: 85%"));
    oled.println();
    oled.println(F("Status: ANALYZING"));
    oled.display();
}

void UIManager::drawSystemStatusOLED() {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setCursor(0, 0);
    oled.println(F("System Status"));
    oled.println();
    oled.print(F("Temp: "));
    oled.print(systemMgr.getTemperature());
    oled.println(F("C"));
    oled.print(F("Battery: "));
    oled.print(map(systemMgr.getBatteryVoltage(), 6000, 8400, 0, 100));
    oled.println(F("%"));
    oled.print(F("Uptime: "));
    oled.print(systemMgr.getUptime() / 60000);
    oled.println(F("min"));
    oled.println();
    oled.println(F("Status: NORMAL"));
    oled.display();
}

void UIManager::showErrorOLED(uint16_t errorCode, const String& message) {
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setCursor(0, 0);
    oled.println(F("SYSTEM ERROR"));
    oled.println();
    oled.print(F("Code: 0x"));
    oled.println(errorCode, HEX);
    oled.println();
    oled.println(message.substring(0, 40)); // 限制长度
    oled.display();
}

// 处理滑块输入 (根据用户TJC配置修正)
void UIManager::processSliderInput(uint8_t sliderType, uint8_t sliderValue) {
    switch(sliderType) {
        case 0x01: // 扫描速度滑块 (h20) - 用户配置0-100，直接使用
            {
                sendTJCText("recon.t29", String(sliderValue) + "%");
                commMgr.setScanSpeed(sliderValue);

                DEBUG_PRINT(F("扫描速度调节: "));
                DEBUG_PRINTLN(sliderValue);
            }
            break;

        case 0x02: // 功率滑块 (h31) - 用户配置0-20，直接使用
            {
                sendTJCText("jamming.t38", String(sliderValue) + "dBm");
                commMgr.sendPowerCommand(sliderValue);

                DEBUG_PRINT(F("功率调节: "));
                DEBUG_PRINTLN(sliderValue);
            }
            break;

        case 0x03: // 频率滑块 (h30) - TJC输出0-255，需要转换
            {
                uint16_t frequency = map(sliderValue, 0, 255, 2402, 2461);
                sendTJCText("jamming.t36", String(frequency) + "MHz");
                commMgr.sendFrequencyCommand(frequency);

                DEBUG_PRINT(F("频率调节: "));
                DEBUG_PRINT(sliderValue);
                DEBUG_PRINT(F(" -> "));
                DEBUG_PRINTLN(frequency);
            }
            break;

        case 0x04: // 带宽滑块 (h32) - 用户配置5-40，直接使用
            {
                sendTJCText("jamming.t40", String(sliderValue) + "MHz");
                commMgr.setBandwidth(sliderValue);

                DEBUG_PRINT(F("带宽调节: "));
                DEBUG_PRINTLN(sliderValue);
            }
            break;

        default:
            DEBUG_PRINT(F("未知滑块类型: "));
            DEBUG_PRINTLN(sliderType);
            break;
    }
}

// 更新主菜单状态
void UIManager::updateMainMenuStatus() {
    // 更新状态栏
    String statusText = "[READY] 系统就绪 | 温度: " + String(systemMgr.getTemperature()) + "°C";
    sendTJCText("main.t1", statusText);

    // 更新电池状态
    uint8_t batteryPercent = systemMgr.getBatteryPercent();
    sendTJCText("main.t2", String(batteryPercent) + "%");
    sendTJCValue("main.j1", batteryPercent);

    DEBUG_PRINTLN(F("主菜单状态已更新"));
}

// 更新信号侦察页面内容
void UIManager::updateReconPageContent() {
    if(currentPage != PAGE_SIGNAL_RECON) return;

    // 更新检测结果
    String detectionResult = "[WAIT] 等待检测信号...\r\n准备开始频谱扫描\r\n系统状态: 就绪";
    sendTJCText("recon.t27", detectionResult);

    // 更新状态栏
    String statusText = "[READY] 系统就绪";
    sendTJCText("recon.t28", statusText);

    DEBUG_PRINTLN(F("侦察页面内容已更新"));
}

// 更新干扰训练页面内容 (根据简化设计)
void UIManager::updateJammingPageContent() {
    if(currentPage != PAGE_JAMMING_TRAINING) return;

    // 更新目标信息
    String targetInfo = "[TARGET]\r\nFreq: --\r\nPower: --\r\nQuality: --\r\nStatus: [WAIT]";
    sendTJCText("jamming.t32", targetInfo);

    // 更新滑块数值显示
    sendTJCText("jamming.t36", "2431MHz");  // 频率默认值
    sendTJCText("jamming.t38", "10dBm");    // 功率默认值
    sendTJCText("jamming.t40", "20MHz");    // 带宽默认值

    // 更新干扰状态
    sendTJCText("jamming.t42", "干扰状态: [READY] 准备就绪");
    sendTJCText("jamming.t43", "持续时间: 00:00:00  目标锁定: ✓");

    // 更新效果监控 (简化为多行文本)
    String effectText = "[EFFECT] 干扰效果监控\r\n等待干扰开始...";
    sendTJCText("jamming.t45", effectText);

    // 更新状态栏
    String statusText = "[JAMMING] 干扰准备  |  效果: --  |  TIME: 00:00  |  TEMP: " + String(systemMgr.getTemperature()) + "C";
    sendTJCText("jamming.t46", statusText);

    DEBUG_PRINTLN(F("干扰页面内容已更新"));
}

// 更新数据分析页面内容
void UIManager::updateDataPageContent() {
    if(currentPage != PAGE_DATA_ANALYSIS) return;

    // 更新会话概览
    String overview = "日期: --  时长: --  模式: --\r\n评分: --  成功率: --  目标: --";
    sendTJCText("data.t52", overview);

    // 更新详细统计
    String detailStats = "平均干扰效率: --     最高效率: --\r\n";
    detailStats += "信号衰减程度: --    响应时间: --\r\n";
    detailStats += "数据包拦截: --     命中精度: --\r\n";
    detailStats += "频率切换次数: --      功率调节: --\r\n\r\n";
    detailStats += "干扰持续时间: --   平均功率: --\r\n";
    detailStats += "目标信号强度: --  最终信号: --\r\n";
    detailStats += "干扰带宽: --      覆盖范围: --\r\n";
    detailStats += "系统稳定性: --      温度范围: --";
    sendTJCText("data.t54", detailStats);

    // 更新训练评估
    String evaluation = "综合评分: --\r\n干扰效果: --\r\n操作熟练度: --\r\n系统稳定性: --\r\n\r\n改进建议: --";
    sendTJCText("data.t56", evaluation);

    // 更新状态栏
    String statusText = "[ANALYSIS] 分析完成  |  [15] 条记录  |  当前: 第3条  |  A级评分";
    sendTJCText("data.t57", statusText);

    DEBUG_PRINTLN(F("数据页面内容已更新"));
}

// 更新系统设置页面内容
void UIManager::updateSettingsPageContent() {
    if(currentPage != PAGE_SYSTEM_SETTINGS) return;

    // 更新射频参数
    String rfParams = "频率范围: 2402-2461MHz\r\n";
    rfParams += "默认功率: " + String(systemMgr.getDefaultPower()) + "dBm\r\n";
    rfParams += "扫描速度: 中等\r\n";
    rfParams += "安全限制: 启用";
    sendTJCText("settings.t62", rfParams);

    // 更新系统配置
    String sysConfig = "蜂鸣器: " + String(systemMgr.isBeeperEnabled() ? "开启" : "关闭") + "\r\n";
    sysConfig += "背光: 80%\r\n";  // 假数据显示
    sysConfig += "温度单位: 摄氏度\r\n";
    sysConfig += "自动保存: " + String(systemMgr.isAutoSaveEnabled() ? "开启" : "关闭") + "\r\n";
    sysConfig += "数据上限: " + String(systemMgr.getDataLimit()) + "条";
    sendTJCText("settings.t64", sysConfig);

    // 更新关于系统
    String aboutInfo = "版本: v2.0.0\r\n";
    aboutInfo += "硬件: UNO R4 WiFi\r\n";
    aboutInfo += "副控: MEGA 2560\r\n";
    aboutInfo += "内存: " + String(systemMgr.getUsedMemory()) + "/2.0KB\r\n";
    aboutInfo += "运行: " + systemMgr.getUptimeString();
    sendTJCText("settings.t68", aboutInfo);

    // 更新系统状态
    String sysStatus = "主控温度: " + String(systemMgr.getMainTemp()) + "°C    副控温度: " + String(systemMgr.getSlaveTemp()) + "°C    电池: " + String(systemMgr.getBatteryPercent()) + "%\r\n";
    sysStatus += "射频状态: " + systemMgr.getRFStatus() + "    通信状态: " + systemMgr.getCommStatus() + "    存储: " + String(systemMgr.getStoragePercent()) + "%\r\n";
    sysStatus += "最后训练: " + systemMgr.getLastTrainingTime() + "    总训练: " + String(systemMgr.getTotalTrainings()) + "次";
    sendTJCText("settings.t70", sysStatus);

    // 更新状态栏
    String statusBar = "[CONFIG] 配置就绪  |  [6] 项设置  |  [AUTO] 自动保存  |  [OK] 已应用";
    sendTJCText("settings.t71", statusBar);

    DEBUG_PRINTLN(F("设置页面内容已更新"));
}

// 显示错误信息
void UIManager::showError(uint16_t errorCode, const String& description) {
    // 跳转到错误页面
    switchTJCPage(PAGE_ERROR_DISPLAY);

    // 构建完整错误信息
    String errorInfo = "错误代码: 0x" + String(errorCode, HEX) + "\r\n\r\n";
    errorInfo += "错误描述: " + description + "\r\n\r\n";
    errorInfo += "建议操作:\r\n";
    errorInfo += getErrorSolution(errorCode) + "\r\n\r\n";
    errorInfo += "时间: " + systemMgr.getCurrentTimeString() + "\r\n";
    errorInfo += "系统状态: " + systemMgr.getSystemStatusString();

    // 更新错误信息显示
    sendTJCText("error.t81", errorInfo);

    // 更新状态栏
    String statusBar = "[ERROR] 系统异常  |  请按确定返回主菜单  |  CODE: 0x" + String(errorCode, HEX);
    sendTJCText("error.t82", statusBar);

    DEBUG_PRINT(F("显示错误: 0x"));
    DEBUG_PRINT(errorCode, HEX);
    DEBUG_PRINT(F(" - "));
    DEBUG_PRINTLN(description);
}

// 获取错误解决方案
String UIManager::getErrorSolution(uint16_t errorCode) {
    switch(errorCode) {
        case 0x0001:
            return "1. 检查EEPROM连接\r\n2. 重启系统\r\n3. 恢复默认配置";
        case 0x0002:
            return "1. 检查串口连接\r\n2. 重启副控\r\n3. 检查通信线路";
        case 0x0003:
            return "1. 立即充电\r\n2. 降低功率\r\n3. 停止训练";
        case 0x0004:
            return "1. 检查散热\r\n2. 降低功率\r\n3. 停止训练";
        default:
            return "1. 记录错误代码\r\n2. 重启系统\r\n3. 联系技术支持";
    }
}

// 检测当前页面状态
void UIManager::checkCurrentPage() {
    // 发送查询当前页面命令
    sendTJCCommand("sendme");

    // 读取响应来判断当前页面
    String response;
    if(readTJCResponse(response)) {
        updateCurrentPageStatus(response);
    }
}

// 读取TJC响应
bool UIManager::readTJCResponse(String& response) {
    unsigned long startTime = millis();
    response = "";

    while(millis() - startTime < 100) { // 100ms超时
        if(tjcSerial.available()) {
            char c = tjcSerial.read();
            if(c == 0xFF) {
                // 检查是否是完整的结束符
                if(tjcSerial.available() >= 2) {
                    if(tjcSerial.read() == 0xFF && tjcSerial.read() == 0xFF) {
                        return true;
                    }
                }
            } else {
                response += c;
            }
        }
    }
    return false;
}

// 更新当前页面状态
void UIManager::updateCurrentPageStatus(const String& response) {
    PageType newPage = currentPage;

    // 根据响应判断当前页面
    if(response.indexOf("main") >= 0) {
        newPage = PAGE_MAIN_MENU;
    } else if(response.indexOf("recon") >= 0) {
        newPage = PAGE_SIGNAL_RECON;
    } else if(response.indexOf("jamming") >= 0) {
        newPage = PAGE_JAMMING_TRAINING;
    } else if(response.indexOf("data") >= 0) {
        newPage = PAGE_DATA_ANALYSIS;
    } else if(response.indexOf("settings") >= 0) {
        newPage = PAGE_SYSTEM_SETTINGS;
    } else if(response.indexOf("error") >= 0) {
        newPage = PAGE_ERROR_DISPLAY;
    }

    // 如果页面发生变化，更新状态和OLED显示
    if(newPage != currentPage) {
        previousPage = currentPage;
        currentPage = newPage;

        // 更新OLED显示
        updateOLEDForCurrentPage();

        // 更新页面内容
        updateCurrentPageContent();

        DEBUG_PRINT(F("页面切换: "));
        DEBUG_PRINT(previousPage);
        DEBUG_PRINT(F(" -> "));
        DEBUG_PRINTLN(currentPage);
    }
}

// 根据当前页面更新OLED显示
void UIManager::updateOLEDForCurrentPage() {
    switch(currentPage) {
        case PAGE_MAIN_MENU:
            setOLEDDisplay(OLED_SYSTEM_STATUS);
            break;
        case PAGE_SIGNAL_RECON:
            setOLEDDisplay(OLED_SIGNAL_ANALYSIS);
            break;
        case PAGE_JAMMING_TRAINING:
            setOLEDDisplay(OLED_JAMMING_EFFECT);
            break;
        case PAGE_DATA_ANALYSIS:
        case PAGE_SYSTEM_SETTINGS:
        case PAGE_ERROR_DISPLAY:
            setOLEDDisplay(OLED_SYSTEM_STATUS);
            break;
        default:
            break;
    }
}

// 更新当前页面内容
void UIManager::updateCurrentPageContent() {
    switch(currentPage) {
        case PAGE_MAIN_MENU:
            updateMainMenuStatus();
            break;
        case PAGE_SIGNAL_RECON:
            updateReconPageContent();
            break;
        case PAGE_JAMMING_TRAINING:
            updateJammingPageContent();
            break;
        case PAGE_DATA_ANALYSIS:
            updateDataPageContent();
            break;
        case PAGE_SYSTEM_SETTINGS:
            updateSettingsPageContent();
            break;
        default:
            break;
    }
}

// 主循环更新函数
void UIManager::update() {
    // 处理触摸事件
    handleTouchEvent();

    // 定期检查页面状态
    static unsigned long lastPageCheck = 0;
    if(millis() - lastPageCheck >= 500) { // 每500ms检查一次
        lastPageCheck = millis();
        checkCurrentPage();
    }

    // 定期更新当前页面内容
    static unsigned long lastContentUpdate = 0;
    if(millis() - lastContentUpdate >= 1000) { // 每1秒更新一次
        lastContentUpdate = millis();
        updateCurrentPageContent();
    }

    // 更新OLED显示
    updateOLEDDisplay();
}

// 修正显示数据分析界面
void UIManager::showDataAnalysis() {
    switchTJCPage(PAGE_DATA_ANALYSIS);

    // 设置页面标题
    sendTJCText("data.t40", "[DATA] 训练数据分析");

    // 显示会话概览
    sendTJCText("data.t41", "Date: 2024-01-16  Duration: 05:23  Mode: NOISE\rScore: A (92)  Success: 85%");

    // 显示统计数据
    sendTJCText("data.t42", "Avg Power: 12.5dBm  Max Power: 18.2dBm\rResponse: 15ms  Stability: 98%\rPackets: 1250  Accuracy: 92%");

    // 更新OLED显示
    setOLEDDisplay(OLED_SYSTEM_STATUS);
}

// 修正显示系统设置界面
void UIManager::showSystemSettings() {
    switchTJCPage(PAGE_SYSTEM_SETTINGS);

    // 设置页面标题
    sendTJCText("settings.t50", "[SET] 系统设置");

    // 射频参数区
    sendTJCText("settings.t51", "[RF PARAMS]\rFreq: 2402-2461MHz\rDefault Power: 10dBm\rScan Speed: Medium\rSafety: Enabled");

    // 系统配置区
    sendTJCText("settings.t52", "[SYSTEM]\rBeeper: ON\rBrightness: 80%\rTemp Unit: C\rTime Format: 24H");

    // 训练参数区
    sendTJCText("settings.t53", "[TRAINING]\rDefault Duration: 60s\rAuto Save: ON\rData Limit: 100\rAlgorithm: Standard");

    // 关于系统区
    sendTJCText("settings.t54", "[ABOUT]\rVersion: v2.0.0\rHardware: UNO R4\rMemory: 1.2/2.0KB\rUptime: 02:15:30");

    // 初始化滑块
    sendTJCValue("settings.h50", 10); // 默认功率
    sendTJCValue("settings.h51", 80); // 背光亮度
}

// 修正显示错误界面
void UIManager::showError(uint16_t errorCode) {
    switchTJCPage(PAGE_ERROR_DISPLAY);

    // 设置错误标题
    sendTJCText("error.t60", "[ERROR] 系统错误");

    // 显示错误代码
    sendTJCText("error.t61", "Error Code: 0x" + String(errorCode, HEX));

    // 显示错误描述
    String errorDesc = getErrorDescription(errorCode);
    sendTJCText("error.t62", errorDesc);

    // 更新OLED显示错误信息
    showErrorOLED(errorCode, errorDesc);
}

// 更新干扰效果显示
void UIManager::updateJammingEffect(const JammingEffectData& data) {
    if(currentPage == PAGE_JAMMING_TRAINING) {
        // 更新目标信息
        String targetInfo = "[TARGET]\rFreq: " + String(data.targetFrequency) + "MHz\r";
        targetInfo += "Power: " + String(data.targetSignalAfter, 1) + "dBm\r";
        targetInfo += "Quality: " + String(100 - data.packetLossRate) + "%";
        sendTJCText("jamming.t31", targetInfo);

        // 更新干扰参数
        String jammingInfo = "[JAMMING]\rFreq: " + String(data.jammingFrequency) + "MHz\r";
        jammingInfo += "Power: " + String(data.jammingPower) + "dBm\r";
        jammingInfo += "Mode: " + (data.jammingActive ? "ACTIVE" : "INACTIVE");
        sendTJCText("jamming.t32", jammingInfo);

        // 更新效果进度条
        sendTJCValue("jamming.j30", data.packetLossRate); // 中断率
        sendTJCValue("jamming.j31", data.packetLossRate); // 丢包率
        sendTJCValue("jamming.j32", (int)((data.targetSignalBefore - data.targetSignalAfter) * 2)); // 信号衰减
        sendTJCValue("jamming.j33", (int)data.jammingEfficiency); // 干扰效率

        // 更新效果监控文本
        String effectInfo = "[EFFECT] 干扰效果监控\r";
        effectInfo += "中断率: " + String(data.packetLossRate) + "%  ";
        effectInfo += "丢包率: " + String(data.packetLossRate) + "%\r";
        effectInfo += "信号衰减: " + String(data.targetSignalBefore - data.targetSignalAfter, 1) + "dB  ";
        effectInfo += "效率: " + String(data.jammingEfficiency, 1) + "%";
        sendTJCText("jamming.t33", effectInfo);
    }

    // 同时更新OLED显示
    showJammingEffectOLED(data);
}

// 全局实例
UIManager uiMgr;
