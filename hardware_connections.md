# 硬件连接方案设计

## 1. 主控系统连接 (Arduino UNO R4 WiFi)

### 1.1 引脚分配表

| 模块 | 连接引脚 | 功能 | 备注 |
|------|----------|------|------|
| TJC串口屏 | D0(RX), D1(TX) | 串口通信 | 硬件串口，支持触摸 |
| NRF24L01 | D2(IRQ), D3(CE), D4(CSN), D11(MOSI), D12(MISO), D13(SCK) | SPI通信 | 与副控通信 |
| 状态LED | D5, D6, D7 | 数字输出 | 系统状态指示 |
| 紧急停止按键 | D8 | 数字输入 | 硬件紧急停止 |
| 总开关 | D9 | 数字输入 | 系统总电源开关 |
| 电源监控 | A0 | 模拟输入 | 电池电压监测 |
| 预留接口 | A1-A5, D10 | 模拟/数字输入 | 扩展传感器和接口 |

### 1.2 连接示意图
```
Arduino UNO R4 WiFi
├── TJC串口屏 (Serial + Touch)
│   ├── VCC → 5V
│   ├── GND → GND
│   ├── RX → D1(TX)
│   └── TX → D0(RX)
├── NRF24L01模块 (SPI)
│   ├── VCC → 3.3V
│   ├── GND → GND
│   ├── CE → D3
│   ├── CSN → D4
│   ├── SCK → D13
│   ├── MOSI → D11
│   ├── MISO → D12
│   └── IRQ → D2
├── 状态指示LED
│   ├── 电源LED → D5
│   ├── 通信LED → D6
│   └── 状态LED → D7
└── 关键控制按键
    ├── 紧急停止 → D8 (带下拉电阻)
    └── 总开关 → D9 (带下拉电阻)
```

## 2. 副控系统连接 (Arduino MEGA 2560)

### 2.1 引脚分配表

| 模块 | 连接引脚 | 功能 | 备注 |
|------|----------|------|------|
| OLED显示屏 | D20(SDA), D21(SCL) | I2C通信 | SSD1315控制器 |
| NRF24L01 | D2(IRQ), D3(CE), D4(CSN), D51(MOSI), D50(MISO), D52(SCK) | SPI通信 | 与主控通信 |
| MCP4725 DAC | D20(SDA), D21(SCL) | I2C通信 | 模拟信号输出 |
| VCO控制 | D5(PWM) | PWM输出 | 频率控制 |
| 功放控制 | D6(PWM) | PWM输出 | 功率控制 |
| 滤波器控制 | D7, D8, D9 | 数字输出 | 频段选择 |
| 信号监测 | A0-A7 | 模拟输入 | 功率/频率监测 |
| 状态LED | D10-D13 | 数字输出 | 模块状态指示 |

### 2.2 射频链路连接
```
Arduino MEGA 2560
├── OLED显示屏 (I2C)
│   ├── VCC → 3.3V
│   ├── GND → GND
│   ├── SDA → D20
│   └── SCL → D21
├── MCP4725 DAC (I2C)
│   ├── VCC → 5V
│   ├── GND → GND
│   ├── SDA → D20
│   ├── SCL → D21
│   └── OUT → VCO控制输入
├── NRF24L01模块 (SPI)
│   ├── VCC → 3.3V
│   ├── GND → GND
│   ├── CE → D3
│   ├── CSN → D4
│   ├── SCK → D52
│   ├── MOSI → D51
│   ├── MISO → D50
│   └── IRQ → D2
└── 射频信号链
    ├── VCO压控振荡器
    │   ├── 电源 → 5V/GND
    │   ├── 控制电压 → MCP4725输出
    │   ├── 频率控制 → D5(PWM)
    │   └── RF输出 → 带通滤波器输入
    ├── 带通滤波器
    │   ├── 电源 → 5V/GND
    │   ├── 频段选择 → D7,D8,D9
    │   ├── RF输入 → VCO输出
    │   └── RF输出 → 功率放大器输入
    └── 功率放大器
        ├── 电源 → 5V/GND
        ├── 功率控制 → D6(PWM)
        ├── RF输入 → 滤波器输出
        └── RF输出 → 天线
```

## 3. 电源系统设计

### 3.1 电源需求分析
| 模块 | 工作电压 | 工作电流 | 峰值电流 | 备注 |
|------|----------|----------|----------|------|
| Arduino UNO R4 | 5V | 50mA | 200mA | 含WiFi模块 |
| Arduino MEGA | 5V | 80mA | 300mA | 多IO口 |
| TJC串口屏 | 5V | 100mA | 200mA | 背光开启 |
| OLED显示屏 | 3.3V | 20mA | 30mA | 低功耗 |
| NRF24L01×2 | 3.3V | 15mA×2 | 115mA×2 | 发射时峰值 |
| MCP4725 | 5V | 1mA | 5mA | 低功耗DAC |
| VCO模块 | 5V | 50mA | 100mA | 射频模块 |
| 滤波器 | 5V | 10mA | 20mA | 被动器件 |
| 功放模块 | 5V | 100mA | 500mA | 最大功耗 |

### 3.2 电源方案
```
电源系统架构：
7.4V锂电池组
├── 5V稳压模块 (3A输出)
│   ├── 主控Arduino UNO R4
│   ├── 副控Arduino MEGA 2560
│   ├── TJC串口屏
│   ├── MCP4725 DAC
│   ├── VCO模块
│   ├── 滤波器模块
│   └── 功放模块
└── 3.3V稳压模块 (1A输出)
    ├── OLED显示屏
    └── NRF24L01模块×2
```

## 4. 机械结构设计

### 4.1 外壳布局
```
便携式外壳 (建议尺寸: 25×15×8cm)
├── 顶部面板
│   ├── TJC串口屏 (主显示)
│   ├── 状态指示LED
│   └── 控制按键
├── 侧面板
│   ├── 电源开关
│   ├── 充电接口
│   ├── 天线接口
│   └── 扩展接口
└── 内部布局
    ├── 主控板区域
    ├── 副控板区域
    ├── 射频模块区域
    ├── 电源模块区域
    └── 散热风扇
```

### 4.2 屏蔽设计
- 射频模块独立屏蔽腔体
- 数字电路与射频电路物理隔离
- 电源模块EMI滤波
- 外壳整体接地设计

## 5. 安全保护设计

### 5.1 硬件保护
- 过流保护：各模块独立保险丝
- 过压保护：TVS二极管保护
- 反接保护：电源反接保护电路
- 温度保护：温度传感器监控

### 5.2 射频安全
- 功率限制：硬件限制最大输出功率
- 频段锁定：硬件限制工作频段
- 紧急停止：硬件紧急停止开关
- 天线检测：天线连接状态检测

## 6. 调试接口

### 6.1 测试点设计
- 各模块电源测试点
- 关键信号测试点
- 射频信号监测点
- 通信信号监测点

### 6.2 调试接口
- UART调试接口
- SWD/JTAG接口
- 示波器探头接口
- 频谱仪测试接口
