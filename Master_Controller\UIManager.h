/*
 * 用户界面管理器 - 负责TJC触摸屏交互和新增OLED显示
 * 实现优化后的卡片式界面设计
 */

#ifndef UI_MANAGER_H
#define UI_MANAGER_H

#include <Arduino.h>
#include <SoftwareSerial.h>
#include <Adafruit_SSD1306.h>
#include <Wire.h>
#include "Config.h"

// TJC触摸屏页面定义
enum UIPage {
    PAGE_STARTUP = 0,
    PAGE_MAIN_MENU = 1,
    PAGE_SIGNAL_RECON = 2,
    PAGE_JAMMING_TRAINING = 3,
    PAGE_DATA_ANALYSIS = 4,
    PAGE_SYSTEM_SETTINGS = 5,
    PAGE_ERROR_DISPLAY = 6
};

// TJC组件ID定义
enum ComponentID {
    // 主菜单组件
    COMP_BTN_RECON = 1,
    COMP_BTN_JAMMING = 2,
    COMP_BTN_DATA = 3,
    COMP_BTN_SETTINGS = 4,
    
    // 侦察界面组件
    COMP_BTN_START_RECON = 10,
    COMP_BTN_STOP_RECON = 11,
    COMP_BTN_START_JAMMING = 12,
    COMP_SLIDER_SCAN_SPEED = 13,
    COMP_RADIO_FULL_SCAN = 14,
    COMP_RADIO_FREQ_SCAN = 15,
    
    // 训练界面组件
    COMP_BTN_PAUSE_TRAINING = 20,
    COMP_BTN_STOP_TRAINING = 21,
    COMP_SLIDER_POWER = 22,
    COMP_SLIDER_FREQUENCY = 23,
    COMP_DROPDOWN_MODE = 24,
    
    // 设置界面组件
    COMP_SLIDER_DEFAULT_POWER = 30,
    COMP_SLIDER_SCAN_SPEED_SET = 31,
    COMP_CHECKBOX_BEEPER = 32,
    COMP_SLIDER_BRIGHTNESS = 33,
    COMP_BTN_SAVE_SETTINGS = 34,
    COMP_BTN_RESET_SETTINGS = 35
};

// 触摸事件结构
struct TouchEvent {
    uint8_t pageId;
    uint8_t componentId;
    uint16_t value;
    unsigned long timestamp;
};

// OLED显示内容类型
enum OLEDDisplayType {
    OLED_JAMMING_EFFECT,
    OLED_SIGNAL_ANALYSIS,
    OLED_SYSTEM_STATUS,
    OLED_ERROR_MESSAGE
};

// 干扰效果数据
struct JammingEffectData {
    float targetSignalBefore;    // 干扰前信号强度
    float targetSignalAfter;     // 干扰后信号强度
    uint16_t packetLossRate;     // 丢包率 (%)
    float jammingEfficiency;     // 干扰效率 (%)
    uint32_t targetFrequency;    // 目标频率
    uint32_t jammingFrequency;   // 干扰频率
    uint16_t jammingPower;       // 干扰功率
    bool jammingActive;          // 干扰状态
};

class UIManager {
private:
    // TJC触摸屏
    SoftwareSerial tjcSerial;
    UIPage currentPage;
    UIPage previousPage;
    bool touchLocked;
    unsigned long lastTouchTime;
    
    // 新增OLED显示屏
    Adafruit_SSD1306 oled;
    OLEDDisplayType currentOLEDDisplay;
    unsigned long lastOLEDUpdate;
    
    // 界面状态
    bool systemInitialized;
    uint8_t initProgress;
    String statusMessage;
    
    // 触摸事件处理
    TouchEvent lastTouchEvent;
    bool touchEventPending;
    
    // 私有方法 - TJC控制
    void sendTJCCommand(const String& command);
    void sendTJCValue(const String& component, int value);
    void sendTJCText(const String& component, const String& text);
    bool readTJCResponse(String& response);
    void switchTJCPage(UIPage page);
    
    // 私有方法 - OLED控制
    void initOLED();
    void updateOLEDDisplay();
    void drawJammingEffect(const JammingEffectData& data);
    void drawSignalAnalysis();
    void drawSystemStatus();
    void drawErrorMessage(uint16_t errorCode);
    
    // 私有方法 - 界面更新
    void updateMainMenuStatus();
    void updateReconInterface();
    void updateTrainingInterface();
    void updateDataAnalysis();
    void updateSystemSettings();
    
    // 私有方法 - 事件处理
    void handleMainMenuTouch(uint8_t componentId);
    void handleReconTouch(uint8_t componentId);
    void handleTrainingTouch(uint8_t componentId);
    void handleDataTouch(uint8_t componentId);
    void handleSettingsTouch(uint8_t componentId);
    
public:
    // 构造函数
    UIManager();
    
    // 初始化和更新
    bool init();
    void update();
    
    // 页面控制
    void showStartupScreen();
    void showMainMenu();
    void showSignalRecon();
    void showJammingTraining();
    void showDataAnalysis();
    void showSystemSettings();
    void showError(uint16_t errorCode, const String& description);

    // 页面内容更新
    void updateMainMenuStatus();
    void updateReconPageContent();
    void updateJammingPageContent();
    void updateDataPageContent();
    void updateSettingsPageContent();
    void updateCurrentPageContent();

    // 页面状态管理
    void checkCurrentPage();
    bool readTJCResponse(String& response);
    void updateCurrentPageStatus(const String& response);
    void updateOLEDForCurrentPage();
    String getErrorSolution(uint16_t errorCode);
    
    // 触摸事件处理
    void handleTouchEvent();
    bool processTouchInput();
    void processTouchInput(uint8_t pageId, uint8_t componentId);
    void processSliderInput(uint8_t sliderType, uint8_t sliderValue);
    void lockInterface(bool locked);
    bool isInterfaceLocked() const { return touchLocked; }
    
    // 状态更新
    void updateSystemStatus(const SystemStatus& status);
    void updateSlaveStatus(bool connected);
    void updateTrainingProgress(uint8_t progress);
    void updateJammingEffect(const JammingEffectData& data);
    void updateSignalData(uint32_t freq, int16_t rssi, float quality);
    
    // OLED显示控制
    void setOLEDDisplay(OLEDDisplayType type);
    void showJammingEffectOLED(const JammingEffectData& data);
    void showSignalAnalysisOLED();
    void showSystemStatusOLED();
    void showErrorOLED(uint16_t errorCode, const String& message);
    
    // 界面反馈
    void showMessage(const String& message, uint16_t duration = 2000);
    void showWarning(const String& warning);
    void showSuccess(const String& message);
    void showProgress(uint8_t percentage, const String& text = "");
    
    // 参数设置界面
    void updateFrequencySlider(uint32_t frequency);
    void updatePowerSlider(uint16_t power);
    void updateModeSelection(uint8_t mode);
    void updateScanSpeed(uint8_t speed);
    
    // 数据显示
    void displayTrainingData(const TrainingData& data);
    void displaySpectrumData(const uint16_t* spectrum, uint8_t points);
    void displayStatistics(uint16_t sessions, float avgScore, uint32_t totalTime);
    
    // 系统控制界面
    void showEmergencyStop();
    void showLowBatteryWarning();
    void showOverTemperatureWarning();
    void showCommLostWarning();
    
    // 调试功能
    bool testDisplay();
    void printUIStatus() const;
    UIPage getCurrentPage() const { return currentPage; }

    // 界面配置
    void setBrightness(uint8_t brightness);
    void setBeeper(bool enabled);
    void setAutoLock(bool enabled, uint16_t timeout = 30000);

    // 错误处理
    String getErrorDescription(uint16_t errorCode);

    // OLED绘制函数
    void drawJammingEffectOLED();
    void drawSignalAnalysisOLED();
    void drawSystemStatusOLED();
};

// 全局用户界面管理器实例声明
extern UIManager uiMgr;

#endif // UI_MANAGER_H
