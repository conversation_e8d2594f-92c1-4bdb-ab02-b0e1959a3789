/*
 * OLED显示测试程序
 * 验证OLED显示屏的基本功能和I2C连接
 */

#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

// OLED显示屏配置
#define OLED_WIDTH 128
#define OLED_HEIGHT 64
#define OLED_RESET -1
#define OLED_I2C_ADDRESS 0x3C

// 创建OLED实例
Adafruit_SSD1306 oled(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);

void setup() {
    Serial.begin(115200);
    Serial.println("=== OLED显示测试程序 ===");
    
    // 初始化I2C
    Wire.begin();
    
    // 初始化OLED显示屏
    if(!oled.begin(SSD1306_SWITCHCAPVCC, OLED_I2C_ADDRESS)) {
        Serial.println("OLED初始化失败!");
        Serial.println("请检查:");
        Serial.println("1. I2C连接 (SDA→A4, SCL→A5)");
        Serial.println("2. 电源连接 (VCC→3.3V, GND→GND)");
        Serial.println("3. I2C地址 (默认0x3C)");
        while(1); // 停止程序
    }
    
    Serial.println("OLED初始化成功!");
    
    // 显示启动信息
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);
    oled.println("OLED Test Program");
    oled.println("128x64 Display");
    oled.println();
    oled.println("I2C Address: 0x3C");
    oled.println("SDA: A4, SCL: A5");
    oled.display();
    
    delay(3000);
}

void loop() {
    // 测试1: 基本文本显示
    testBasicText();
    delay(2000);
    
    // 测试2: 大字体显示
    testLargeText();
    delay(2000);
    
    // 测试3: 图形绘制
    testGraphics();
    delay(2000);
    
    // 测试4: 模拟传感器数据显示
    testSensorDisplay();
    delay(2000);
    
    // 测试5: 滚动文本
    testScrolling();
    delay(2000);
}

void testBasicText() {
    Serial.println("测试1: 基本文本显示");
    
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);
    oled.println("Basic Text Test");
    oled.println("Line 2");
    oled.println("Line 3");
    oled.println("Line 4");
    oled.println("Line 5");
    oled.println("Line 6");
    oled.println("Line 7");
    oled.println("Line 8");
    oled.display();
}

void testLargeText() {
    Serial.println("测试2: 大字体显示");
    
    oled.clearDisplay();
    oled.setTextSize(2);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);
    oled.println("Large");
    oled.println("Text");
    oled.setTextSize(1);
    oled.println("Normal size again");
    oled.display();
}

void testGraphics() {
    Serial.println("测试3: 图形绘制");
    
    oled.clearDisplay();
    
    // 绘制矩形
    oled.drawRect(0, 0, 127, 63, SSD1306_WHITE);
    oled.drawRect(10, 10, 107, 43, SSD1306_WHITE);
    
    // 绘制线条
    oled.drawLine(0, 0, 127, 63, SSD1306_WHITE);
    oled.drawLine(127, 0, 0, 63, SSD1306_WHITE);
    
    // 绘制圆形
    oled.drawCircle(64, 32, 20, SSD1306_WHITE);
    
    oled.display();
}

void testSensorDisplay() {
    Serial.println("测试4: 模拟传感器数据显示");
    
    // 模拟传感器数据
    float temperature = 25.6;
    float humidity = 65.2;
    int battery = 85;
    
    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    
    // 标题
    oled.setCursor(0, 0);
    oled.println("Sensor Data");
    oled.drawLine(0, 10, 127, 10, SSD1306_WHITE);
    
    // 温度
    oled.setCursor(0, 16);
    oled.print("Temp: ");
    oled.print(temperature);
    oled.println("C");
    
    // 湿度
    oled.setCursor(0, 26);
    oled.print("Humidity: ");
    oled.print(humidity);
    oled.println("%");
    
    // 电池
    oled.setCursor(0, 36);
    oled.print("Battery: ");
    oled.print(battery);
    oled.println("%");
    
    // 状态
    oled.setCursor(0, 46);
    oled.println("Status: NORMAL");
    
    // 运行时间
    oled.setCursor(0, 56);
    oled.print("Uptime: ");
    oled.print(millis() / 1000);
    oled.println("s");
    
    oled.display();
}

void testScrolling() {
    Serial.println("测试5: 滚动文本 (修复版)");

    oled.clearDisplay();
    oled.setTextSize(1);
    oled.setTextColor(SSD1306_WHITE);
    oled.setCursor(0, 0);
    oled.println("Scrolling Test");
    oled.println("This text will");
    oled.println("scroll left and");
    oled.println("right to test");
    oled.println("the display");
    oled.println("scrolling");
    oled.println("capability");
    oled.display();

    delay(1000);

    // 向左滚动 (修复: 使用正确的方法名)
    Serial.println("向左滚动...");
    oled.startscrollleft(0x00, 0x0F);
    delay(3000);
    oled.stopscroll();

    delay(500);

    // 向右滚动 (修复: 使用正确的方法名)
    Serial.println("向右滚动...");
    oled.startscrollright(0x00, 0x0F);
    delay(3000);
    oled.stopscroll();

    delay(500);

    // 对角滚动 (如果支持)
    Serial.println("对角滚动...");
    oled.startscrolldiagright(0x00, 0x07);
    delay(3000);
    oled.stopscroll();

    Serial.println("滚动测试完成");
}
