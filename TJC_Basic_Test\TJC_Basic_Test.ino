/*
 * TJC触摸屏基础通信测试程序
 * 验证Arduino与TJC屏幕的基本串口通信功能
 * 
 * 硬件连接:
 * TJC屏幕 → Arduino UNO R4 WiFi
 * VCC → 5V
 * GND → GND  
 * TX  → D2 (SoftwareSerial RX)
 * RX  → D3 (SoftwareSerial TX)
 * 
 * TJC屏幕设置:
 * 波特率: 9600
 * 界面: 简单的文本显示控件
 */

#include <SoftwareSerial.h>

// TJC屏幕串口配置
#define TJC_RX_PIN 2
#define TJC_TX_PIN 3
#define TJC_BAUD_RATE 9600

// 创建软串口实例
SoftwareSerial tjcSerial(TJC_RX_PIN, TJC_TX_PIN);

// 测试变量
int testCounter = 0;
unsigned long lastSendTime = 0;
unsigned long lastHeartbeat = 0;
bool verboseMode = false; // 详细输出模式开关

void setup() {
    Serial.begin(115200);
    Serial.println("=== TJC触摸屏基础通信测试 ===");
    Serial.println("Arduino UNO R4 WiFi → TJC屏幕");
    Serial.println();
    
    // 初始化TJC串口
    tjcSerial.begin(TJC_BAUD_RATE);
    Serial.print("TJC串口初始化完成，波特率: ");
    Serial.println(TJC_BAUD_RATE);
    
    delay(2000); // 等待TJC屏幕启动
    
    // 发送初始化命令
    Serial.println("发送TJC初始化命令...");
    sendTJCCommand("page 0"); // 切换到页面0
    delay(100);
    
    // 测试基本文本显示
    Serial.println("开始通信测试...");
    sendTJCText("t0", "TJC Test Ready");
    sendTJCText("t1", "Arduino Connected");
    
    Serial.println("初始化完成，开始循环测试");
    Serial.println();
}

void loop() {
    // 每10秒发送一次测试数据 (降低频率)
    if(millis() - lastSendTime >= 10000) {
        lastSendTime = millis();
        testBasicCommunication();
    }

    // 每30秒发送一次心跳 (降低频率)
    if(millis() - lastHeartbeat >= 30000) {
        lastHeartbeat = millis();
        sendHeartbeat();
    }

    // 检查TJC屏幕返回的数据
    checkTJCResponse();

    // 处理串口命令
    handleSerialCommands();

    delay(100);
}

void testBasicCommunication() {
    testCounter++;
    
    Serial.print("测试 #");
    Serial.print(testCounter);
    Serial.println(" - 发送数据到TJC屏幕");
    
    // 测试1: 更新计数器显示
    String counterText = "Count: " + String(testCounter);
    sendTJCText("t0", counterText);
    
    // 测试2: 更新时间显示
    unsigned long uptime = millis() / 1000;
    String timeText = "Uptime: " + String(uptime) + "s";
    sendTJCText("t1", timeText);
    
    // 测试3: 更新状态显示
    String statusText = "Status: " + String(testCounter % 2 == 0 ? "EVEN" : "ODD");
    sendTJCText("t2", statusText);
    
    // 测试4: 数值显示
    sendTJCNumber("n0", testCounter);
    sendTJCNumber("n1", uptime);
    
    Serial.println("数据发送完成");
}

void sendHeartbeat() {
    Serial.println("发送心跳信号...");
    sendTJCCommand("get sleep"); // 查询屏幕状态
    sendTJCText("t3", "Heartbeat: " + String(millis() / 1000));
}

void sendTJCCommand(String command) {
    tjcSerial.print(command);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);

    // 只在手动命令时显示详细信息
    static bool showDetails = false;
    if(showDetails || command.startsWith("page") || command.startsWith("dim")) {
        Serial.print("发送命令: ");
        Serial.println(command);
    }
}

void sendTJCText(String controlName, String text) {
    String command = controlName + ".txt=\"" + text + "\"";
    tjcSerial.print(command);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);

    // 简化输出，只显示关键信息
    // Serial.print("设置文本 ");
    // Serial.print(controlName);
    // Serial.print(": ");
    // Serial.println(text);
}

void sendTJCNumber(String controlName, int value) {
    String command = controlName + ".val=" + String(value);
    tjcSerial.print(command);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);

    // 简化输出
    // Serial.print("设置数值 ");
    // Serial.print(controlName);
    // Serial.print(": ");
    // Serial.println(value);
}

void sendTJCProgress(String controlName, int value) {
    // 进度条控件，值范围0-100
    value = constrain(value, 0, 100);
    String command = controlName + ".val=" + String(value);
    tjcSerial.print(command);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    tjcSerial.write(0xFF);
    
    Serial.print("设置进度条 ");
    Serial.print(controlName);
    Serial.print(": ");
    Serial.print(value);
    Serial.println("%");
}

void checkTJCResponse() {
    if(tjcSerial.available()) {
        Serial.print("TJC返回数据: ");
        
        String response = "";
        while(tjcSerial.available()) {
            char c = tjcSerial.read();
            if(c == 0xFF) {
                Serial.print("[FF]");
            } else if(c >= 32 && c <= 126) {
                Serial.print(c);
                response += c;
            } else {
                Serial.print("[");
                Serial.print((int)c, HEX);
                Serial.print("]");
            }
        }
        Serial.println();
        
        // 解析触摸事件
        if(response.length() > 0) {
            parseTouchEvent(response);
        }
    }
}

void parseTouchEvent(String data) {
    Serial.print("解析触摸事件: ");
    Serial.println(data);
    
    // 简单的触摸事件解析
    if(data.indexOf("65 00") >= 0) {
        Serial.println("检测到按钮按下事件");
        handleButtonPress();
    } else if(data.indexOf("65 01") >= 0) {
        Serial.println("检测到按钮释放事件");
        handleButtonRelease();
    }
}

void handleButtonPress() {
    Serial.println("处理按钮按下");
    sendTJCText("t4", "Button Pressed!");
    
    // 可以在这里添加具体的按钮响应逻辑
}

void handleButtonRelease() {
    Serial.println("处理按钮释放");
    sendTJCText("t4", "Button Released");
}

void handleSerialCommands() {
    if(Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        Serial.print("收到串口命令: ");
        Serial.println(command);
        
        if(command == "test") {
            testBasicCommunication();
        } else if(command == "clear") {
            sendTJCCommand("cls BLACK");
        } else if(command == "page0") {
            sendTJCCommand("page 0");
        } else if(command == "page1") {
            sendTJCCommand("page 1");
        } else if(command == "bright") {
            sendTJCCommand("dim=100");
        } else if(command == "dim") {
            sendTJCCommand("dim=30");
        } else if(command == "sleep") {
            sendTJCCommand("sleep=1");
        } else if(command == "wake") {
            sendTJCCommand("sleep=0");
        } else if(command.startsWith("text:")) {
            String text = command.substring(5);
            sendTJCText("t0", text);
        } else if(command.startsWith("num:")) {
            int num = command.substring(4).toInt();
            sendTJCNumber("n0", num);
        } else if(command == "verbose") {
            verboseMode = !verboseMode;
            Serial.print("详细模式: ");
            Serial.println(verboseMode ? "开启" : "关闭");
        } else if(command == "help") {
            printHelp();
        } else {
            Serial.println("未知命令，输入 'help' 查看帮助");
        }
    }
}

void printHelp() {
    Serial.println("=== TJC测试命令帮助 ===");
    Serial.println("test     - 执行通信测试");
    Serial.println("clear    - 清屏");
    Serial.println("page0    - 切换到页面0");
    Serial.println("page1    - 切换到页面1");
    Serial.println("bright   - 设置亮度100%");
    Serial.println("dim      - 设置亮度30%");
    Serial.println("sleep    - 屏幕休眠");
    Serial.println("wake     - 屏幕唤醒");
    Serial.println("text:xxx - 发送文本到t0控件");
    Serial.println("num:123  - 发送数值到n0控件");
    Serial.println("verbose  - 切换详细输出模式");
    Serial.println("help     - 显示此帮助");
    Serial.println("========================");
}
