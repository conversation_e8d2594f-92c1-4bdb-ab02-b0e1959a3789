/*
 * 系统管理器 - 负责系统状态管理、硬件监控、错误处理
 */

#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <Arduino.h>
#include "Config.h"

class SystemManager {
private:
    // 系统状态
    SystemState currentState;
    SystemState previousState;
    unsigned long stateChangeTime;
    
    // 系统监控数据
    unsigned long systemStartTime;
    uint16_t batteryVoltage;
    int8_t systemTemperature;
    uint16_t currentErrorCode;
    
    // LED状态
    bool powerLEDState;
    bool commLEDState;
    bool errorLEDState;
    unsigned long lastLEDUpdate;
    
    // 性能监控
    unsigned long loopStartTime;
    unsigned long maxLoopTime;
    unsigned long totalLoops;
    
    // 私有方法
    void updateBatteryVoltage();
    void updateTemperature();
    void updateLEDs();
    void checkSystemHealth();
    int freeMemory();
    
public:
    // 构造函数
    SystemManager();
    
    // 初始化和更新
    bool init();
    void update();
    
    // 状态管理
    void setState(SystemState newState);
    SystemState getState() const { return currentState; }
    SystemState getPreviousState() const { return previousState; }
    String getStateString() const;
    unsigned long getStateTime() const;
    
    // 系统信息
    SystemStatus getSystemStatus() const;
    unsigned long getUptime() const;
    uint16_t getBatteryVoltage() const { return batteryVoltage; }
    int8_t getTemperature() const { return systemTemperature; }

    // UIManager需要的扩展函数
    uint8_t getBatteryPercent() const;
    uint8_t getDefaultPower() const;
    bool isBeeperEnabled() const;
    bool isAutoSaveEnabled() const;
    uint16_t getDataLimit() const;
    float getUsedMemory() const;
    String getUptimeString() const;
    int8_t getMainTemp() const;
    int8_t getSlaveTemp() const;
    String getRFStatus() const;
    String getCommStatus() const;
    uint8_t getStoragePercent() const;
    String getLastTrainingTime() const;
    uint16_t getTotalTrainings() const;
    String getCurrentTimeString() const;
    String getSystemStatusString() const;
    
    // 错误管理
    void setErrorCode(uint16_t errorCode);
    uint16_t getErrorCode() const { return currentErrorCode; }
    void clearError();
    String getErrorString(uint16_t errorCode) const;
    
    // LED控制
    void setPowerLED(bool state);
    void setCommLED(bool state);
    void setErrorLED(bool state);
    void toggleLED(uint8_t ledNumber);
    bool testLEDs();
    
    // 系统控制
    void emergencyStop();
    void softReset();
    void shutdown();
    
    // 性能监控
    void startPerformanceMonitor();
    void endPerformanceMonitor();
    void printPerformanceStats() const;
    
    // 系统健康检查
    bool isSystemHealthy() const;
    bool isBatteryLow() const;
    bool isOverTemperature() const;
    
    // 调试功能
    void printSystemInfo() const;
    void runDiagnostics();
};

// 全局系统管理器实例声明
extern SystemManager systemMgr;

#endif // SYSTEM_MANAGER_H
