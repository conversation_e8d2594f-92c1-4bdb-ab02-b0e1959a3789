/*
 * 通信管理器 - 负责主副控串口通信和敌方信号模拟
 * 更新：使用串口通信替代NRF24L01，WiFi+RF24模拟敌方通信
 */

#ifndef COMM_MANAGER_H
#define COMM_MANAGER_H

#include <Arduino.h>
#include <SoftwareSerial.h>
#include <WiFi.h>
#include "Config.h"

// 串口通信数据包结构
struct CommPacket {
    uint8_t header;          // 包头 0xAA
    uint8_t cmdId;           // 命令ID
    uint8_t dataLength;      // 数据长度 (0-16)
    uint8_t data[MAX_DATA_LENGTH]; // 数据载荷
    uint8_t checksum;        // 校验和
    uint8_t footer;          // 包尾 0x55
};

// 命令ID定义
enum CommandID {
    CMD_HEARTBEAT = 0x01,
    CMD_SET_FREQUENCY = 0x10,
    CMD_SET_POWER = 0x11,
    CMD_SET_MODE = 0x12,
    CMD_START_JAMMING = 0x13,
    CMD_STOP_JAMMING = 0x14,
    CMD_GET_STATUS = 0x20,
    CMD_GET_RF_PARAMS = 0x21,
    CMD_EMERGENCY_STOP = 0xFF
};

// 副控状态数据结构
struct SlaveStatus {
    bool online;
    uint32_t currentFreq;
    uint16_t currentPower;
    uint8_t jammingMode;
    bool jammingActive;
    int8_t temperature;
    uint16_t errorCode;
    unsigned long lastUpdate;
};

// 敌方信号模拟数据
struct EnemySignal {
    bool active;
    uint32_t frequency;
    int16_t rssi;
    uint16_t dataRate;
    uint32_t packetsTotal;
    uint32_t packetsLost;
    float signalQuality;
    unsigned long lastPacketTime;
};

class CommManager {
private:
    // 串口通信
    SoftwareSerial slaveSerial;
    uint8_t sequenceNumber;
    unsigned long lastHeartbeat;
    unsigned long lastCommCheck;
    
    // 通信缓冲区
    CommPacket txBuffer;
    CommPacket rxBuffer;
    uint8_t rxBufferIndex;
    bool packetReceiving;
    
    // 副控状态
    SlaveStatus slaveStatus;
    bool slaveConnected;
    
    // 敌方信号模拟
    EnemySignal enemySignal;
    WiFiServer enemyServer;
    WiFiClient enemyClient;
    unsigned long lastEnemyPacket;
    uint32_t enemyPacketCounter;
    
    // 私有方法
    void initSlaveComm();
    void initEnemySimulation();
    bool sendPacket(const CommPacket& packet);
    bool receivePacket();
    void processReceivedPacket(const CommPacket& packet);
    uint8_t calculateChecksum(const CommPacket& packet);
    bool validatePacket(const CommPacket& packet);
    
    // 敌方信号模拟方法
    void updateEnemySignal();
    void simulateEnemyTraffic();
    void analyzeJammingEffect();
    
public:
    // 构造函数
    CommManager();
    
    // 初始化和更新
    bool init();
    void update();
    
    // 主副控通信
    bool sendCommand(uint8_t cmdId, const uint8_t* data = nullptr, uint8_t dataLen = 0);
    bool sendFrequencyCommand(uint32_t frequency);
    bool sendPowerCommand(uint16_t power);
    bool sendModeCommand(uint8_t mode);
    bool sendJammingControl(bool start);
    bool requestSlaveStatus();
    
    // 状态查询
    bool isSlaveConnected() const { return slaveConnected; }
    SlaveStatus getSlaveStatus() const { return slaveStatus; }
    unsigned long getLastCommTime() const;
    
    // 敌方信号模拟
    bool startEnemySimulation();
    void stopEnemySimulation();
    bool isEnemySignalActive() const { return enemySignal.active; }
    EnemySignal getEnemySignalStatus() const { return enemySignal; }
    
    // 干扰效果分析
    float getJammingEfficiency() const;
    uint16_t getPacketLossRate() const;
    float getSignalDegradation() const;
    
    // 紧急控制
    void emergencyStop();
    void disconnect();
    
    // 调试功能
    void printCommStatus() const;
    void printEnemyStatus() const;
    bool testSlaveConnection();
};

// 全局通信管理器实例声明
extern CommManager commMgr;

#endif // COMM_MANAGER_H
