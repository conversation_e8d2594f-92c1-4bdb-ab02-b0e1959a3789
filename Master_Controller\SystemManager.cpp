/*
 * 系统管理器实现 - 系统状态管理、硬件监控、错误处理
 */

#include "SystemManager.h"
#include <EEPROM.h>

// 全局系统管理器实例
SystemManager systemMgr;

// 构造函数
SystemManager::SystemManager() :
    currentState(SYS_INIT),
    previousState(SYS_INIT),
    stateChangeTime(0),
    systemStartTime(0),
    batteryVoltage(7400),  // 默认7.4V
    systemTemperature(25), // 默认25°C
    currentErrorCode(0),
    powerLEDState(false),
    commLEDState(false),
    errorLEDState(false),
    lastLEDUpdate(0),
    loopStartTime(0),
    maxLoopTime(0),
    totalLoops(0)
{
}

// 初始化函数
bool SystemManager::init() {
    DEBUG_PRINTLN(F("SystemManager: 开始初始化"));
    
    // 记录系统启动时间
    systemStartTime = millis();
    stateChangeTime = systemStartTime;
    
    // 初始化LED引脚
    pinMode(STATUS_LED_POWER, OUTPUT);
    pinMode(STATUS_LED_COMM, OUTPUT);
    pinMode(STATUS_LED_ERROR, OUTPUT);
    
    // 初始化ADC用于电池电压监测
    analogReference(DEFAULT);
    
    // 初始化EEPROM (如果需要)
    // EEPROM配置管理将在后续实现
    
    // 初始状态设置
    setState(SYS_READY);
    setPowerLED(true);  // 电源LED常亮
    
    // 初始系统检查
    updateBatteryVoltage();
    updateTemperature();
    
    DEBUG_PRINTLN(F("SystemManager: 初始化完成"));
    return true;
}

// 主循环更新函数
void SystemManager::update() {
    // 性能监控开始
    startPerformanceMonitor();
    
    // 定期更新系统状态 (每1秒)
    static unsigned long lastStatusUpdate = 0;
    if(millis() - lastStatusUpdate >= 1000) {
        lastStatusUpdate = millis();
        
        updateBatteryVoltage();
        updateTemperature();
        checkSystemHealth();
    }
    
    // 更新LED状态 (每100ms)
    if(millis() - lastLEDUpdate >= 100) {
        lastLEDUpdate = millis();
        updateLEDs();
    }
    
    // 性能监控结束
    endPerformanceMonitor();
}

// 状态管理
void SystemManager::setState(SystemState newState) {
    if(newState != currentState) {
        previousState = currentState;
        currentState = newState;
        stateChangeTime = millis();
        
        DEBUG_PRINT(F("系统状态变更: "));
        DEBUG_PRINT((int)previousState);
        DEBUG_PRINT(F(" -> "));
        DEBUG_PRINTLN((int)currentState);
    }
}

String SystemManager::getStateString() const {
    switch(currentState) {
        case SYS_INIT: return "初始化";
        case SYS_READY: return "就绪";
        case SYS_TRAINING: return "训练中";
        case SYS_ERROR: return "错误";
        case SYS_SHUTDOWN: return "关机";
        default: return "未知";
    }
}

unsigned long SystemManager::getStateTime() const {
    return millis() - stateChangeTime;
}

// 系统信息获取
SystemStatus SystemManager::getSystemStatus() const {
    SystemStatus status;
    status.state = currentState;
    status.uptime = getUptime();
    status.batteryVoltage = batteryVoltage;
    status.temperature = systemTemperature;
    status.errorCode = currentErrorCode;
    status.slaveConnected = false; // 将由CommManager更新
    status.timestamp = millis();
    return status;
}

unsigned long SystemManager::getUptime() const {
    return millis() - systemStartTime;
}

// 错误管理
void SystemManager::setErrorCode(uint16_t errorCode) {
    currentErrorCode = errorCode;
    if(errorCode != 0) {
        setState(SYS_ERROR);
        setErrorLED(true);
        
        DEBUG_PRINT(F("系统错误: 0x"));
        DEBUG_PRINTLN(errorCode, HEX);
    }
}

void SystemManager::clearError() {
    currentErrorCode = 0;
    setErrorLED(false);
    if(currentState == SYS_ERROR) {
        setState(SYS_READY);
    }
    DEBUG_PRINTLN(F("错误已清除"));
}

String SystemManager::getErrorString(uint16_t errorCode) const {
    switch(errorCode) {
        case 0x0001: return "配置管理器初始化失败";
        case 0x0002: return "副控通信超时";
        case 0x0003: return "电池电压过低";
        case 0x0004: return "系统温度过高";
        case 0x0010: return "TJC显示屏通信失败";
        case 0x0020: return "OLED显示屏初始化失败";
        case 0x0030: return "射频模块异常";
        case 0x0040: return "存储器错误";
        default: return "未知错误";
    }
}

// LED控制
void SystemManager::setPowerLED(bool state) {
    powerLEDState = state;
    digitalWrite(STATUS_LED_POWER, state ? HIGH : LOW);
}

void SystemManager::setCommLED(bool state) {
    commLEDState = state;
    digitalWrite(STATUS_LED_COMM, state ? HIGH : LOW);
}

void SystemManager::setErrorLED(bool state) {
    errorLEDState = state;
    digitalWrite(STATUS_LED_ERROR, state ? HIGH : LOW);
}

void SystemManager::toggleLED(uint8_t ledNumber) {
    switch(ledNumber) {
        case 1: setPowerLED(!powerLEDState); break;
        case 2: setCommLED(!commLEDState); break;
        case 3: setErrorLED(!errorLEDState); break;
    }
}

bool SystemManager::testLEDs() {
    DEBUG_PRINTLN(F("LED测试开始"));
    
    // 依次点亮每个LED
    setPowerLED(true);
    delay(200);
    setCommLED(true);
    delay(200);
    setErrorLED(true);
    delay(200);
    
    // 全部关闭
    setPowerLED(false);
    setCommLED(false);
    setErrorLED(false);
    delay(200);
    
    // 恢复正常状态
    setPowerLED(true);  // 电源LED常亮
    
    DEBUG_PRINTLN(F("LED测试完成"));
    return true;
}

// 系统控制
void SystemManager::emergencyStop() {
    DEBUG_PRINTLN(F("紧急停止！"));
    setState(SYS_ERROR);
    setErrorCode(ERROR_EMERGENCY_STOP);
    
    // 关闭所有输出
    setPowerLED(false);
    setCommLED(false);
    setErrorLED(true);
    
    // 这里应该通知其他管理器停止操作
    // 将在集成时实现
}

void SystemManager::softReset() {
    DEBUG_PRINTLN(F("软件重启"));
    
    // 清除错误状态
    clearError();
    
    // 重置状态
    setState(SYS_INIT);
    delay(100);
    setState(SYS_READY);
    
    // 重置LED状态
    setPowerLED(true);
    setCommLED(false);
    setErrorLED(false);
}

void SystemManager::shutdown() {
    DEBUG_PRINTLN(F("系统关机"));
    setState(SYS_SHUTDOWN);
    
    // 关闭所有LED
    setPowerLED(false);
    setCommLED(false);
    setErrorLED(false);
    
    // 这里应该保存重要数据到EEPROM
    // 将在配置管理功能中实现
}

// 性能监控
void SystemManager::startPerformanceMonitor() {
    loopStartTime = micros();
}

void SystemManager::endPerformanceMonitor() {
    unsigned long loopTime = micros() - loopStartTime;
    if(loopTime > maxLoopTime) {
        maxLoopTime = loopTime;
    }
    totalLoops++;
}

void SystemManager::printPerformanceStats() const {
    DEBUG_PRINT(F("性能统计 - 最大循环时间: "));
    DEBUG_PRINT(maxLoopTime);
    DEBUG_PRINT(F("μs, 总循环次数: "));
    DEBUG_PRINTLN(totalLoops);
}

// 系统健康检查
bool SystemManager::isSystemHealthy() const {
    return (currentErrorCode == 0 &&
            !isBatteryLow() &&
            !isOverTemperature());
}

bool SystemManager::isBatteryLow() const {
    return batteryVoltage < MIN_BATTERY_VOLTAGE;
}

bool SystemManager::isOverTemperature() const {
    return systemTemperature > MAX_TEMPERATURE;
}

// UIManager需要的具体函数实现
uint8_t SystemManager::getBatteryPercent() const {
    // 将电压转换为百分比 (6.0V-8.4V对应0-100%)
    uint8_t percent = map(batteryVoltage, 6000, 8400, 0, 100);
    return constrain(percent, 0, 100);
}

uint8_t SystemManager::getDefaultPower() const {
    // 返回默认功率设置 (从配置中读取，暂时返回固定值)
    return 10; // 10dBm
}

bool SystemManager::isBeeperEnabled() const {
    // 返回蜂鸣器使能状态 (从配置中读取，暂时返回固定值)
    return true;
}

bool SystemManager::isAutoSaveEnabled() const {
    // 返回自动保存使能状态 (从配置中读取，暂时返回固定值)
    return true;
}

uint16_t SystemManager::getDataLimit() const {
    // 返回数据存储上限 (从配置中读取，暂时返回固定值)
    return 100;
}

float SystemManager::getUsedMemory() const {
    // 计算已使用内存 (简化实现)
    // Arduino UNO R4 WiFi有32KB Flash, 8KB SRAM
    // 这里返回一个估算值
    return 1.2; // KB
}

String SystemManager::getUptimeString() const {
    unsigned long uptime = getUptime();
    unsigned long hours = uptime / 3600000;
    unsigned long minutes = (uptime % 3600000) / 60000;
    unsigned long seconds = (uptime % 60000) / 1000;

    String result = "";
    if(hours < 10) result += "0";
    result += String(hours) + ":";
    if(minutes < 10) result += "0";
    result += String(minutes) + ":";
    if(seconds < 10) result += "0";
    result += String(seconds);

    return result;
}

int8_t SystemManager::getMainTemp() const {
    // 主控温度 (当前实现返回系统温度)
    return systemTemperature;
}

int8_t SystemManager::getSlaveTemp() const {
    // 副控温度 (暂时返回模拟值，实际应从CommManager获取)
    return systemTemperature - 3; // 假设副控温度稍低
}

String SystemManager::getRFStatus() const {
    // 射频状态 (暂时返回固定值，实际应从CommManager获取)
    return "正常";
}

String SystemManager::getCommStatus() const {
    // 通信状态 (暂时返回固定值，实际应从CommManager获取)
    return "在线";
}

uint8_t SystemManager::getStoragePercent() const {
    // 存储使用百分比 (暂时返回固定值)
    return 45;
}

String SystemManager::getLastTrainingTime() const {
    // 最后训练时间 (暂时返回固定值，实际应从配置中读取)
    return "2024-01-16 14:30";
}

uint16_t SystemManager::getTotalTrainings() const {
    // 总训练次数 (暂时返回固定值，实际应从配置中读取)
    return 25;
}

String SystemManager::getCurrentTimeString() const {
    // 当前时间字符串 (简化实现，使用运行时间)
    unsigned long currentTime = millis();
    unsigned long hours = (currentTime / 3600000) % 24;
    unsigned long minutes = (currentTime % 3600000) / 60000;
    unsigned long seconds = (currentTime % 60000) / 1000;

    String result = "2024-01-16 ";
    if(hours < 10) result += "0";
    result += String(hours) + ":";
    if(minutes < 10) result += "0";
    result += String(minutes) + ":";
    if(seconds < 10) result += "0";
    result += String(seconds);

    return result;
}

String SystemManager::getSystemStatusString() const {
    String status = "主控: " + getStateString();
    status += ", 温度: " + String(systemTemperature) + "°C";
    status += ", 电池: " + String(getBatteryPercent()) + "%";

    if(currentErrorCode != 0) {
        status += ", 错误: 0x" + String(currentErrorCode, HEX);
    }

    return status;
}

// 调试功能
void SystemManager::printSystemInfo() const {
    DEBUG_PRINTLN(F("=== 系统信息 ==="));
    DEBUG_PRINT(F("状态: "));
    DEBUG_PRINTLN(getStateString());
    DEBUG_PRINT(F("运行时间: "));
    DEBUG_PRINTLN(getUptimeString());
    DEBUG_PRINT(F("温度: "));
    DEBUG_PRINT(systemTemperature);
    DEBUG_PRINTLN(F("°C"));
    DEBUG_PRINT(F("电池电压: "));
    DEBUG_PRINT(batteryVoltage);
    DEBUG_PRINTLN(F("mV"));
    DEBUG_PRINT(F("电池百分比: "));
    DEBUG_PRINT(getBatteryPercent());
    DEBUG_PRINTLN(F("%"));
    DEBUG_PRINT(F("错误代码: 0x"));
    DEBUG_PRINTLN(currentErrorCode, HEX);
    DEBUG_PRINTLN(F("==============="));
}

void SystemManager::runDiagnostics() {
    DEBUG_PRINTLN(F("开始系统诊断..."));

    // LED测试
    testLEDs();

    // 电池电压检查
    updateBatteryVoltage();
    if(isBatteryLow()) {
        DEBUG_PRINTLN(F("警告: 电池电压过低"));
    }

    // 温度检查
    updateTemperature();
    if(isOverTemperature()) {
        DEBUG_PRINTLN(F("警告: 系统温度过高"));
    }

    // 内存检查
    DEBUG_PRINT(F("可用内存: "));
    DEBUG_PRINT(freeMemory());
    DEBUG_PRINTLN(F(" bytes"));

    // 性能统计
    printPerformanceStats();

    DEBUG_PRINTLN(F("系统诊断完成"));
}

// 私有方法实现
void SystemManager::updateBatteryVoltage() {
    // 读取电池电压 (通过ADC)
    // 假设使用分压电路，需要根据实际硬件调整
    int adcValue = analogRead(BATTERY_MONITOR_PIN);

    // 转换为实际电压 (mV)
    // Arduino UNO R4 WiFi: 3.3V参考电压，12位ADC
    // 假设分压比为1:2 (实际需要根据硬件确定)
    batteryVoltage = (adcValue * 3300 * 2) / 4095;

    // 限制在合理范围内
    batteryVoltage = constrain(batteryVoltage, 5000, 9000);

    // 如果电压过低，设置错误
    if(isBatteryLow() && currentErrorCode == 0) {
        setErrorCode(0x0003); // 电池电压过低
    }
}

void SystemManager::updateTemperature() {
    // 读取系统温度
    // 这里使用简化的模拟实现
    // 实际应该读取温度传感器 (如DS18B20, DHT22等)

    // 模拟温度变化 (基于运行时间和负载)
    unsigned long uptime = getUptime();
    int baseTemp = 25; // 基础温度25°C
    int tempVariation = (uptime / 60000) % 10; // 每分钟变化，最大10°C

    systemTemperature = baseTemp + tempVariation;

    // 如果温度过高，设置错误
    if(isOverTemperature() && currentErrorCode == 0) {
        setErrorCode(0x0004); // 系统温度过高
    }
}

void SystemManager::updateLEDs() {
    // 根据系统状态更新LED显示
    switch(currentState) {
        case SYS_INIT:
            // 初始化时LED闪烁
            setPowerLED((millis() / 500) % 2);
            break;

        case SYS_READY:
            // 就绪时电源LED常亮
            setPowerLED(true);
            break;

        case SYS_TRAINING:
            // 训练时通信LED闪烁
            setCommLED((millis() / 200) % 2);
            break;

        case SYS_ERROR:
            // 错误时错误LED闪烁
            setErrorLED((millis() / 300) % 2);
            break;

        case SYS_SHUTDOWN:
            // 关机时所有LED关闭
            setPowerLED(false);
            setCommLED(false);
            setErrorLED(false);
            break;
    }
}

void SystemManager::checkSystemHealth() {
    // 检查系统健康状态
    bool wasHealthy = isSystemHealthy();

    // 检查各项指标
    bool batteryOK = !isBatteryLow();
    bool tempOK = !isOverTemperature();

    // 如果之前健康但现在不健康，记录问题
    if(wasHealthy && !isSystemHealthy()) {
        if(!batteryOK) {
            DEBUG_PRINTLN(F("系统健康检查: 电池电压过低"));
        }
        if(!tempOK) {
            DEBUG_PRINTLN(F("系统健康检查: 温度过高"));
        }
    }

    // 如果系统恢复健康，清除相关错误
    if(batteryOK && tempOK && currentErrorCode != 0) {
        if(currentErrorCode == 0x0003 || currentErrorCode == 0x0004) {
            clearError();
            DEBUG_PRINTLN(F("系统健康检查: 系统状态恢复正常"));
        }
    }
}

// 内存检查辅助函数
int SystemManager::freeMemory() {
    // 简化的内存检查实现
    // 实际项目中可以使用更精确的方法
    extern int __heap_start, *__brkval;
    int v;
    return (int) &v - (__brkval == 0 ? (int) &__heap_start : (int) __brkval);
}
