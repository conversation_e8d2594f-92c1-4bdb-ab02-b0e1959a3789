/*
 * 副控串口通信测试程序
 * Arduino MEGA 2560 副控端
 * 
 * 测试目标:
 * 1. 验证115200波特率串口通信
 * 2. 测试指令接收和状态发送
 * 3. 验证数据包格式和校验
 * 4. 模拟各种工作状态
 */

// ==================== 通信协议配置 ====================
#define MASTER_SERIAL Serial1   // 硬件串口1与主控通信
#define MASTER_BAUD_RATE 115200 // 通信波特率

// 通信协议定义
#define COMM_START_BYTE 0xAA
#define COMM_END_BYTE 0x55
#define COMM_MAX_DATA_LEN 16

// 指令类型定义 (与主控保持一致)
enum MasterCommand {
    CMD_SET_FREQUENCY = 0x01,    // 设置干扰频率
    CMD_SET_POWER = 0x02,        // 设置干扰功率
    CMD_START_JAMMING = 0x03,    // 开始干扰
    CMD_STOP_JAMMING = 0x04,     // 停止干扰
    CMD_START_SCAN = 0x05,       // 开始侦察
    CMD_STOP_SCAN = 0x06,        // 停止侦察
    CMD_GET_STATUS = 0x07,       // 获取状态
    CMD_RESET = 0x08             // 重置副控
};

// 状态反馈类型 (与主控保持一致)
enum SlaveStatus {
    STATUS_READY = 0x01,         // 就绪
    STATUS_JAMMING = 0x02,       // 干扰中
    STATUS_SCANNING = 0x03,      // 侦察中
    STATUS_ERROR = 0x04          // 错误
};

// ==================== 系统状态 ====================
struct SystemState {
    SlaveStatus currentStatus;
    float currentFrequency;      // 当前频率 (MHz)
    uint8_t currentPower;        // 当前功率 (0-255)
    bool isJamming;
    bool isScanning;
    unsigned long lastCommandTime;
    unsigned long statusChangeTime;
} sysState;

// ==================== 测试统计 ====================
struct TestStats {
    unsigned long totalReceived;  // 总接收指令数
    unsigned long totalSent;      // 总发送响应数
    unsigned long validCommands;  // 有效指令数
    unsigned long invalidCommands; // 无效指令数
    unsigned long checksumErrors; // 校验错误数
    float commandSuccessRate;     // 指令成功率
    unsigned long startTime;      // 测试开始时间
} testStats;

// ==================== 函数声明 ====================
void initializeTest();
void handleMasterCommand();
void processCommand(uint8_t command, uint8_t* data, uint8_t dataLen);
void sendStatusToMaster(SlaveStatus status, uint8_t* data = nullptr, uint8_t dataLen = 0);
uint8_t calculateChecksum(uint8_t* data, uint8_t length);
void updateSystemStatus();
void printSystemStatus();
void printTestStats();

void setup() {
    Serial.begin(115200);
    Serial.println(F("=== 副控串口通信测试程序 ==="));
    Serial.println(F("Arduino MEGA 2560 副控端"));
    Serial.println();
    
    // 初始化测试
    initializeTest();
    
    Serial.println(F("副控测试程序启动完成"));
    Serial.println(F("等待主控指令..."));
    Serial.println(F("输入命令:"));
    Serial.println(F("  status - 显示系统状态"));
    Serial.println(F("  stats  - 显示测试统计"));
    Serial.println(F("  reset  - 重置测试统计"));
    Serial.println();
}

void loop() {
    // 处理主控指令
    handleMasterCommand();
    
    // 处理串口调试命令
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if (command == "status") {
            printSystemStatus();
        } else if (command == "stats") {
            printTestStats();
        } else if (command == "reset") {
            // 重置测试统计
            testStats.totalReceived = 0;
            testStats.totalSent = 0;
            testStats.validCommands = 0;
            testStats.invalidCommands = 0;
            testStats.checksumErrors = 0;
            testStats.startTime = millis();
            Serial.println(F("测试统计已重置"));
        }
    }
    
    // 更新系统状态
    updateSystemStatus();
    
    delay(10);
}

// ==================== 测试函数实现 ====================
void initializeTest() {
    // 初始化串口通信
    MASTER_SERIAL.begin(MASTER_BAUD_RATE);
    
    // 清空串口缓冲区
    while(MASTER_SERIAL.available()) {
        MASTER_SERIAL.read();
    }
    
    // 初始化系统状态
    sysState.currentStatus = STATUS_READY;
    sysState.currentFrequency = 2440.0;
    sysState.currentPower = 0;
    sysState.isJamming = false;
    sysState.isScanning = false;
    sysState.lastCommandTime = 0;
    sysState.statusChangeTime = millis();
    
    // 初始化测试统计
    testStats.totalReceived = 0;
    testStats.totalSent = 0;
    testStats.validCommands = 0;
    testStats.invalidCommands = 0;
    testStats.checksumErrors = 0;
    testStats.commandSuccessRate = 0.0;
    testStats.startTime = millis();
    
    Serial.println(F("串口通信初始化完成 (115200波特率)"));
}

void handleMasterCommand() {
    static uint8_t buffer[32];
    static uint8_t bufferIndex = 0;
    
    while (MASTER_SERIAL.available()) {
        uint8_t byte = MASTER_SERIAL.read();
        
        if (bufferIndex == 0 && byte == COMM_START_BYTE) {
            buffer[bufferIndex++] = byte;
        } else if (bufferIndex > 0) {
            buffer[bufferIndex++] = byte;
            
            // 检查是否接收完整数据包
            if (bufferIndex >= 5 && byte == COMM_END_BYTE) {
                testStats.totalReceived++;
                
                // 解析数据包
                uint8_t command = buffer[1];
                uint8_t dataLength = buffer[2];
                uint8_t checksum = buffer[3 + dataLength];
                
                // 验证校验和
                uint8_t calculatedChecksum = calculateChecksum(&buffer[1], 2 + dataLength);
                if (checksum == calculatedChecksum) {
                    testStats.validCommands++;
                    processCommand(command, &buffer[3], dataLength);
                } else {
                    testStats.checksumErrors++;
                    testStats.invalidCommands++;
                    Serial.println(F("主控指令校验和错误"));
                    sendStatusToMaster(STATUS_ERROR);
                }
                
                // 更新成功率
                if (testStats.totalReceived > 0) {
                    testStats.commandSuccessRate = (float)testStats.validCommands / testStats.totalReceived * 100.0;
                }
                
                bufferIndex = 0;
            } else if (bufferIndex >= 32) {
                // 缓冲区溢出，重置
                bufferIndex = 0;
                testStats.invalidCommands++;
            }
        }
    }
}

void processCommand(uint8_t command, uint8_t* data, uint8_t dataLen) {
    Serial.print(F("收到主控指令: 0x"));
    Serial.print(command, HEX);
    Serial.print(F(", 数据长度: "));
    Serial.println(dataLen);
    
    sysState.lastCommandTime = millis();
    
    switch (command) {
        case CMD_SET_FREQUENCY:
            if (dataLen >= 4) {
                float frequency = *(float*)data;
                sysState.currentFrequency = frequency;
                Serial.print(F("设置频率: "));
                Serial.print(frequency, 2);
                Serial.println(F("MHz"));
                sendStatusToMaster(STATUS_READY);
            } else {
                Serial.println(F("频率设置数据长度错误"));
                sendStatusToMaster(STATUS_ERROR);
            }
            break;
            
        case CMD_SET_POWER:
            if (dataLen >= 1) {
                uint8_t power = data[0];
                sysState.currentPower = power;
                Serial.print(F("设置功率: "));
                Serial.println(power);
                sendStatusToMaster(STATUS_READY);
            } else {
                Serial.println(F("功率设置数据长度错误"));
                sendStatusToMaster(STATUS_ERROR);
            }
            break;
            
        case CMD_START_JAMMING:
            Serial.println(F("开始干扰"));
            sysState.isJamming = true;
            sysState.isScanning = false;
            sysState.currentStatus = STATUS_JAMMING;
            sysState.statusChangeTime = millis();
            // 发送干扰状态和当前频率
            uint8_t jammingData[4];
            *(float*)jammingData = sysState.currentFrequency;
            sendStatusToMaster(STATUS_JAMMING, jammingData, 4);
            break;
            
        case CMD_STOP_JAMMING:
            Serial.println(F("停止干扰"));
            sysState.isJamming = false;
            sysState.currentStatus = STATUS_READY;
            sysState.statusChangeTime = millis();
            sendStatusToMaster(STATUS_READY);
            break;
            
        case CMD_START_SCAN:
            Serial.println(F("开始侦察"));
            sysState.isScanning = true;
            sysState.isJamming = false;
            sysState.currentStatus = STATUS_SCANNING;
            sysState.statusChangeTime = millis();
            sendStatusToMaster(STATUS_SCANNING);
            break;
            
        case CMD_STOP_SCAN:
            Serial.println(F("停止侦察"));
            sysState.isScanning = false;
            sysState.currentStatus = STATUS_READY;
            sysState.statusChangeTime = millis();
            sendStatusToMaster(STATUS_READY);
            break;
            
        case CMD_GET_STATUS:
            Serial.println(F("状态查询"));
            sendStatusToMaster(sysState.currentStatus);
            break;
            
        case CMD_RESET:
            Serial.println(F("重置副控"));
            // 重置所有状态
            sysState.currentStatus = STATUS_READY;
            sysState.currentFrequency = 2440.0;
            sysState.currentPower = 0;
            sysState.isJamming = false;
            sysState.isScanning = false;
            sysState.statusChangeTime = millis();
            sendStatusToMaster(STATUS_READY);
            break;
            
        default:
            Serial.print(F("未知指令: 0x"));
            Serial.println(command, HEX);
            testStats.invalidCommands++;
            sendStatusToMaster(STATUS_ERROR);
            break;
    }
}

void sendStatusToMaster(SlaveStatus status, uint8_t* data, uint8_t dataLen) {
    if (dataLen > COMM_MAX_DATA_LEN) return;
    
    // 构建数据包
    uint8_t packet[6 + dataLen];
    packet[0] = COMM_START_BYTE;
    packet[1] = status;
    packet[2] = dataLen;
    
    // 复制数据
    for (uint8_t i = 0; i < dataLen; i++) {
        packet[3 + i] = data[i];
    }
    
    // 计算校验和
    uint8_t checksumData[2 + dataLen];
    checksumData[0] = packet[1];
    checksumData[1] = packet[2];
    for (uint8_t i = 0; i < dataLen; i++) {
        checksumData[2 + i] = packet[3 + i];
    }
    packet[3 + dataLen] = calculateChecksum(checksumData, 2 + dataLen);
    packet[4 + dataLen] = COMM_END_BYTE;
    
    // 发送数据包
    MASTER_SERIAL.write(packet, 5 + dataLen);
    testStats.totalSent++;
    
    Serial.print(F("发送状态到主控: 0x"));
    Serial.print(status, HEX);
    Serial.print(F(", 数据长度: "));
    Serial.println(dataLen);
}

uint8_t calculateChecksum(uint8_t* data, uint8_t length) {
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

void updateSystemStatus() {
    // 模拟状态变化 (用于测试)
    static unsigned long lastUpdate = 0;
    if (millis() - lastUpdate < 5000) return; // 每5秒更新一次
    lastUpdate = millis();
    
    // 如果正在干扰或侦察，偶尔发送状态更新
    if (sysState.isJamming || sysState.isScanning) {
        if (random(100) < 20) { // 20%概率发送状态更新
            sendStatusToMaster(sysState.currentStatus);
        }
    }
}

void printSystemStatus() {
    Serial.println(F("=== 副控系统状态 ==="));
    Serial.print(F("当前状态: "));
    switch(sysState.currentStatus) {
        case STATUS_READY: Serial.println(F("就绪")); break;
        case STATUS_JAMMING: Serial.println(F("干扰中")); break;
        case STATUS_SCANNING: Serial.println(F("侦察中")); break;
        case STATUS_ERROR: Serial.println(F("错误")); break;
        default: Serial.println(F("未知")); break;
    }
    Serial.print(F("当前频率: "));
    Serial.print(sysState.currentFrequency, 2);
    Serial.println(F("MHz"));
    Serial.print(F("当前功率: "));
    Serial.println(sysState.currentPower);
    Serial.print(F("干扰状态: "));
    Serial.println(sysState.isJamming ? F("开启") : F("关闭"));
    Serial.print(F("侦察状态: "));
    Serial.println(sysState.isScanning ? F("开启") : F("关闭"));
    Serial.print(F("最后指令时间: "));
    Serial.print((millis() - sysState.lastCommandTime) / 1000);
    Serial.println(F("秒前"));
    Serial.println(F("=================="));
}

void printTestStats() {
    Serial.println(F("=== 副控测试统计 ==="));
    Serial.print(F("总接收指令: "));
    Serial.println(testStats.totalReceived);
    Serial.print(F("总发送响应: "));
    Serial.println(testStats.totalSent);
    Serial.print(F("有效指令: "));
    Serial.println(testStats.validCommands);
    Serial.print(F("无效指令: "));
    Serial.println(testStats.invalidCommands);
    Serial.print(F("校验错误: "));
    Serial.println(testStats.checksumErrors);
    Serial.print(F("指令成功率: "));
    Serial.print(testStats.commandSuccessRate, 1);
    Serial.println(F("%"));
    Serial.print(F("运行时间: "));
    Serial.print((millis() - testStats.startTime) / 1000);
    Serial.println(F("秒"));
    Serial.println(F("=================="));
}
