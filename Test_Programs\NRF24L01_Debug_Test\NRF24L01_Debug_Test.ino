/*
 * NRF24L01发送失败专用调试程序
 * 专门用于排查发送失败问题
 * 
 * 常见发送失败原因:
 * 1. 电源供电不足 (最常见)
 * 2. SPI连接问题
 * 3. 地址配置错误
 * 4. 功率设置过高
 * 5. 天线连接问题
 */

#include <SPI.h>
#include <RF24.h>

// ==================== 硬件配置 ====================
#define RF_CE_PIN 9
#define RF_CSN_PIN 10
#define TEST_CHANNEL 40        // 2440MHz
#define TEST_ADDRESS "TEST1"   // 简单地址

RF24 radio(RF_CE_PIN, RF_CSN_PIN);

// ==================== 测试数据 ====================
struct TestPacket {
    uint16_t sequence;
    uint32_t timestamp;
    char message[16];
};

void setup() {
    Serial.begin(115200);
    Serial.println(F("=== NRF24L01发送失败调试程序 ==="));
    Serial.println(F("专门用于排查发送失败问题"));
    Serial.println();
    
    // 延迟等待串口稳定
    delay(1000);
    
    // 执行完整诊断
    performCompleteDiagnostics();
}

void loop() {
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        if (command == "test") {
            Serial.println(F("执行发送测试..."));
            testBasicTransmission();
        } else if (command == "diag") {
            Serial.println(F("执行完整诊断..."));
            performCompleteDiagnostics();
        } else if (command == "power") {
            Serial.println(F("测试不同功率级别..."));
            testPowerLevels();
        } else if (command == "simple") {
            Serial.println(F("执行最简单发送测试..."));
            testSimpleTransmission();
        } else if (command == "reset") {
            Serial.println(F("重新初始化NRF24L01..."));
            initializeRadio();
        } else if (command == "help") {
            printHelp();
        } else {
            Serial.println(F("未知命令，输入help查看帮助"));
        }
    }
    
    delay(100);
}

void printHelp() {
    Serial.println(F("=== 可用命令 ==="));
    Serial.println(F("test   - 执行发送测试"));
    Serial.println(F("diag   - 执行完整诊断"));
    Serial.println(F("power  - 测试不同功率级别"));
    Serial.println(F("simple - 最简单发送测试"));
    Serial.println(F("reset  - 重新初始化"));
    Serial.println(F("help   - 显示帮助"));
    Serial.println();
}

void performCompleteDiagnostics() {
    Serial.println(F("=== 开始完整诊断 ==="));
    
    // 1. 硬件连接检查
    Serial.println(F("1. 检查硬件连接..."));
    checkHardwareConnection();
    
    // 2. SPI通信测试
    Serial.println(F("2. 测试SPI通信..."));
    testSPICommunication();
    
    // 3. 电源供电检查
    Serial.println(F("3. 检查电源供电..."));
    checkPowerSupply();
    
    // 4. 初始化NRF24L01
    Serial.println(F("4. 初始化NRF24L01..."));
    initializeRadio();
    
    // 5. 寄存器状态检查
    Serial.println(F("5. 检查寄存器状态..."));
    checkRegisterStatus();
    
    Serial.println(F("=== 诊断完成 ==="));
    Serial.println();
}

void checkHardwareConnection() {
    Serial.println(F("--- 硬件连接检查 ---"));
    Serial.println(F("请确认以下连接:"));
    Serial.println(F("NRF24L01 -> Arduino UNO R4"));
    Serial.println(F("VCC  -> 3.3V (重要！不是5V)"));
    Serial.println(F("GND  -> GND"));
    Serial.println(F("CE   -> Pin 9"));
    Serial.println(F("CSN  -> Pin 10"));
    Serial.println(F("MOSI -> Pin 11"));
    Serial.println(F("MISO -> Pin 12"));
    Serial.println(F("SCK  -> Pin 13"));
    Serial.println();
    
    Serial.println(F("重要提醒:"));
    Serial.println(F("1. 必须使用3.3V供电，5V会损坏模块"));
    Serial.println(F("2. 连线要短且牢固，避免接触不良"));
    Serial.println(F("3. 建议添加去耦电容 (100uF + 10uF)"));
    Serial.println();
}

void testSPICommunication() {
    Serial.println(F("--- SPI通信测试 ---"));

    // 使用RF24库的公共方法测试连接
    bool isConnected = radio.isChipConnected();
    Serial.print(F("芯片连接状态: "));
    Serial.println(isConnected ? F("✅ 正常") : F("❌ 异常"));

    if (!isConnected) {
        Serial.println(F("❌ SPI通信失败"));
        Serial.println(F("可能原因:"));
        Serial.println(F("1. SPI连线错误"));
        Serial.println(F("2. 模块未正确供电"));
        Serial.println(F("3. 模块损坏"));
        Serial.println(F("请检查连接:"));
        Serial.println(F("  CE  -> Pin 9"));
        Serial.println(F("  CSN -> Pin 10"));
        Serial.println(F("  MOSI -> Pin 11"));
        Serial.println(F("  MISO -> Pin 12"));
        Serial.println(F("  SCK -> Pin 13"));
    } else {
        Serial.println(F("✅ SPI通信正常"));
    }
    Serial.println();
}

void checkPowerSupply() {
    Serial.println(F("--- 电源供电检查 ---"));
    Serial.println(F("NRF24L01功耗特点:"));
    Serial.println(F("- 待机电流: 22uA"));
    Serial.println(F("- 接收电流: 13.5mA"));
    Serial.println(F("- 发送电流: 11.3mA (0dBm)"));
    Serial.println(F("- 峰值电流: 可达20mA"));
    Serial.println();
    
    Serial.println(F("Arduino UNO R4 3.3V输出:"));
    Serial.println(F("- 最大电流: 150mA"));
    Serial.println(F("- 通常足够，但需要稳定"));
    Serial.println();
    
    Serial.println(F("电源问题解决方案:"));
    Serial.println(F("1. 添加大容量电容 (100uF)"));
    Serial.println(F("2. 使用外部3.3V稳压器"));
    Serial.println(F("3. 检查连线电阻"));
    Serial.println();
}

void initializeRadio() {
    Serial.println(F("--- 初始化NRF24L01 ---"));
    
    if (!radio.begin()) {
        Serial.println(F("❌ NRF24L01初始化失败"));
        Serial.println(F("请检查硬件连接和供电"));
        return;
    }
    
    Serial.println(F("✅ NRF24L01初始化成功"));
    
    // 使用最保守的设置
    radio.setPALevel(RF24_PA_MIN);      // 最低功率
    radio.setDataRate(RF24_250KBPS);    // 最低速率
    radio.setChannel(TEST_CHANNEL);     // 测试频道
    radio.setAutoAck(false);            // 关闭自动应答
    radio.setRetries(0, 0);             // 关闭重传
    
    // 设置地址
    uint8_t address[6] = TEST_ADDRESS;
    radio.openWritingPipe(address);
    radio.stopListening();
    
    Serial.println(F("配置参数:"));
    Serial.println(F("- 功率: RF24_PA_MIN (-18dBm)"));
    Serial.println(F("- 速率: RF24_250KBPS"));
    Serial.println(F("- 频道: 40 (2440MHz)"));
    Serial.println(F("- 自动应答: 关闭"));
    Serial.println(F("- 重传: 关闭"));
    Serial.println();
}

void checkRegisterStatus() {
    Serial.println(F("--- 寄存器状态检查 ---"));

    // 检查芯片连接
    bool connected = radio.isChipConnected();
    Serial.print(F("芯片连接状态: "));
    Serial.println(connected ? F("✅ 正常") : F("❌ 异常"));

    if (!connected) {
        Serial.println(F("❌ 无法读取状态寄存器"));
        Serial.println(F("请检查硬件连接和供电"));
        return;
    }

    // 清除可能的错误状态
    Serial.println(F("清除发送和接收FIFO..."));
    radio.flush_tx();
    radio.flush_rx();

    Serial.println(F("✅ 状态检查完成"));
    Serial.println();
}

void testSimpleTransmission() {
    Serial.println(F("--- 最简单发送测试 ---"));

    // 最简单的数据
    uint8_t data = 0x55;

    Serial.println(F("发送单字节数据: 0x55"));
    bool result = radio.write(&data, 1);

    Serial.print(F("发送结果: "));
    Serial.println(result ? F("✅ 成功") : F("❌ 失败"));

    if (result) {
        Serial.println(F("🎉 发送成功！问题已解决"));
        Serial.println(F("可能的解决因素:"));
        Serial.println(F("1. 使用了最低功率设置"));
        Serial.println(F("2. 关闭了自动应答"));
        Serial.println(F("3. 使用了最低数据速率"));
    } else {
        Serial.println(F("🔍 发送仍然失败"));
        Serial.println(F("建议检查:"));
        Serial.println(F("1. 电源供电是否稳定"));
        Serial.println(F("2. 是否添加了去耦电容"));
        Serial.println(F("3. 连线是否牢固"));
        Serial.println(F("4. 模块是否损坏"));
    }

    Serial.println();
}

void testBasicTransmission() {
    Serial.println(F("--- 基础发送测试 ---"));

    TestPacket packet;
    packet.sequence = 1;
    packet.timestamp = millis();
    strcpy(packet.message, "Hello");

    Serial.print(F("发送测试包 ("));
    Serial.print(sizeof(packet));
    Serial.println(F("字节)"));

    bool result = radio.write(&packet, sizeof(packet));

    Serial.print(F("发送结果: "));
    Serial.println(result ? F("✅ 成功") : F("❌ 失败"));

    if (result) {
        Serial.println(F("🎉 发送成功！问题已解决"));
        Serial.println(F("系统工作正常，可以进行后续测试"));
    } else {
        Serial.println(F("🔍 发送失败，可能原因:"));
        Serial.println(F("1. 数据包太大 (尝试减小数据包)"));
        Serial.println(F("2. 电源供电不足"));
        Serial.println(F("3. 自动应答超时"));
        Serial.println(F("4. 硬件连接问题"));

        Serial.println(F("建议: 先确保simple命令能成功"));
    }

    Serial.println();
}

void testPowerLevels() {
    Serial.println(F("--- 功率级别测试 ---"));
    
    rf24_pa_dbm_e powerLevels[] = {RF24_PA_MIN, RF24_PA_LOW, RF24_PA_HIGH, RF24_PA_MAX};
    const char* powerNames[] = {"MIN(-18dBm)", "LOW(-12dBm)", "HIGH(-6dBm)", "MAX(0dBm)"};
    
    uint8_t testData = 0xAA;
    
    for (int i = 0; i < 4; i++) {
        Serial.print(F("测试功率级别: "));
        Serial.println(powerNames[i]);
        
        radio.setPALevel(powerLevels[i]);
        delay(100); // 等待设置生效
        
        bool result = radio.write(&testData, 1);
        Serial.print(F("  发送结果: "));
        Serial.println(result ? F("✅ 成功") : F("❌ 失败"));
        
        delay(500);
    }
    
    // 恢复最低功率
    radio.setPALevel(RF24_PA_MIN);
    Serial.println(F("已恢复最低功率设置"));
    Serial.println();
}
