# 更新后的电子对抗模拟训练系统架构

## 🎯 系统设计理念

基于真实电子对抗环境，实现"侦察→分析→干扰→效果评估"的完整流程，提供更贴近实战的训练体验。

## 📡 通信架构重新设计

### 频率分配策略
```
2400-2410MHz: 模拟敌方通信 (主控WiFi ↔ 副控RF24侦察)
2420-2450MHz: 干扰信号输出 (副控VCO→PA→天线)
2470-2480MHz: 主副控内部通信 (主控RF24 ↔ 副控RF24通信)
```

### 硬件配置更新

#### 主控系统 (Arduino UNO R4 WiFi)
```
硬件模块：
- TJC触摸屏 (用户界面)
- NRF24L01模块 (与副控通信，2470-2480MHz)
- WiFi模块 (模拟敌方发射端，2400-2410MHz)
- 状态LED指示

功能定位：
- 训练场景管理和用户交互
- 模拟敌方通信信号发射
- 与副控的指令协调
- 训练数据记录和分析
```

#### 副控系统 (Arduino MEGA 2560)
```
硬件模块：
- NRF24L01模块1 (与主控通信，2470-2480MHz)
- NRF24L01模块2 (侦察敌方信号，2400-2410MHz)
- OLED显示屏1 (射频状态显示，原有)
- OLED显示屏2 (侦察和干扰效果显示，新增)
- VCO + 滤波器 + PA模块 (干扰信号生成)
- MCP4725 DAC (VCO控制)

功能定位：
- 信号侦察和频谱分析
- 干扰信号生成和控制
- 干扰效果监测和显示
- 实时射频参数监控
```

## 🔍 信号侦察模块设计

### 侦察流程
```
1. 副控RF24模块2监听2400-2410MHz频段
2. 检测到敌方通信信号 (主控WiFi发射)
3. 分析信号特征：频率、功率、调制方式、数据包特征
4. 在OLED2上显示侦察结果
5. 启动干扰信号 (2420-2450MHz)
6. 监测干扰效果并显示
```

### 侦察显示内容
```
OLED2显示内容 (128x64):
┌──────────────────────────────┐
│ Signal Intelligence  [RECON] │
├──────────────────────────────┤
│ Target: 2405MHz WiFi Signal  │
│ Power: -45dBm  Rate: 2Mbps   │
│ Packets: 1250  Lost: 15      │
│ Quality: 98% → 23% (Jammed)  │
├──────────────────────────────┤
│ Jamming: ACTIVE 2435MHz      │
│ Effect: STRONG  Success: 85% │
├──────────────────────────────┤
│ Status: TARGET DISRUPTED     │
└──────────────────────────────┘
```

## 🎛️ 训练场景设计

### 场景1: 基础侦察训练
```
目标：学习信号侦察和识别
流程：
1. 主控发射模拟WiFi信号
2. 副控侦察并识别信号特征
3. 显示信号参数和频谱
4. 评估侦察准确性
```

### 场景2: 干扰效果训练
```
目标：学习干扰技术和效果评估
流程：
1. 建立稳定的模拟通信链路
2. 启动干扰信号
3. 实时监测通信质量变化
4. 调整干扰参数优化效果
```

### 场景3: 对抗博弈训练
```
目标：模拟攻防对抗
流程：
1. 模拟敌方采用抗干扰措施
2. 调整干扰策略应对
3. 评估对抗效果
4. 学习电子对抗战术
```

## 🖥️ 用户界面更新

### TJC触摸屏界面
```
新增页面：
- 侦察控制页面
- 干扰效果分析页面
- 对抗训练场景页面
- 实时频谱显示页面
```

### 双OLED显示系统
```
OLED1 (原有): 射频系统状态
- VCO频率和功率
- PA模块状态
- 系统温度和电源

OLED2 (新增): 侦察和干扰效果
- 目标信号特征
- 干扰前后对比
- 干扰效果评估
- 频谱瀑布图
```

## 🔧 技术实现要点

### 1. 频率隔离技术
```cpp
// 主控WiFi模拟敌方信号
WiFi.begin("Enemy_Network", "password");
WiFi.setChannel(1);  // 2412MHz

// 副控侦察模块
rf24_recon.setChannel(1);  // 2412MHz
rf24_recon.startListening();

// 主副控通信
rf24_comm.setChannel(76);  // 2476MHz

// 干扰信号
vco.setFrequency(2435);  // 2435MHz
```

### 2. 干扰效果量化
```cpp
struct JammingEffect {
    float signalStrengthBefore;  // 干扰前信号强度
    float signalStrengthAfter;   // 干扰后信号强度
    uint16_t packetsLostRate;    // 丢包率 (%)
    uint16_t latencyIncrease;    // 延迟增加 (ms)
    uint8_t jammingEfficiency;   // 干扰效率 (0-100)
};
```

### 3. 实时频谱显示
```cpp
// 简化的频谱分析
void updateSpectrum() {
    for(int freq = 2400; freq <= 2480; freq += 5) {
        rf24_recon.setChannel((freq-2400)/5);
        int rssi = rf24_recon.getRSSI();
        spectrumData[freq-2400] = rssi;
    }
    displaySpectrum(spectrumData);
}
```

## 🎯 方案优势总结

### 教学价值
- ✅ 完整的电子对抗流程体验
- ✅ 可视化的干扰效果展示
- ✅ 真实的对抗环境模拟
- ✅ 多种训练场景支持

### 技术特点
- ✅ 频率分离避免自干扰
- ✅ 双OLED提供丰富信息显示
- ✅ 实时效果监测和评估
- ✅ 去除网络依赖，更安全

### 实战贴近性
- ✅ 侦察→干扰的真实流程
- ✅ 效果评估和战术调整
- ✅ 无网络依赖的独立系统
- ✅ 便携式野外训练适用

## 🛒 硬件采购更新

### 需要新增采购
1. **NRF24L01模块** - 1个 (副控侦察用)
2. **0.96寸OLED显示屏** - 1个 (干扰效果显示)
3. **面包板和杜邦线** - 连接新增模块

### 原有硬件利用
- 所有原计划硬件保持不变
- 增加功能而不替换硬件

这个方案如何？既保持了原有设计的完整性，又增加了更贴近实战的侦察和效果评估功能！
